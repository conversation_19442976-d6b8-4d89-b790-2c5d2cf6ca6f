import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
    ArrowLeft,
    Plus,
    Search,
    MoreHorizontal,
    Edit,
    Trash2,
    Phone,
    MapPin,
    FileText,
    Users,
    TrendingUp,
    Package,
    Loader,
    RefreshCw,
    Download
} from "lucide-react"
import { useGlobalAlert } from "@/components/common/GlobalAlert"
import { SupplierApiService } from "@/services/SupplierApi"
import { useApi } from "@/hooks/useApi"
import EditSupplier from "./EditSupplier"
import AddSupplier from "./AddSupplier"


export default function SupplierManagement() {
    const navigate = useNavigate()
    const alert = useGlobalAlert();

    const [searchTerm, setSearchTerm] = useState("")
    const [showAddDialog, setShowAddDialog] = useState(false)
    const [showEditDialog, setShowEditDialog] = useState(false)
    const [editingSupplierId, setEditingSupplierId] = useState<number | null>(null);

    // Fetch suppliers from API call
    const { data: suppliers = [], refetch: refetchSuppliers, isLoading: isLoading, } = useApi(() => SupplierApiService.getAllSuppliers());

    const filteredSuppliers = suppliers?.filter(supplier =>
        supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.phone.includes(searchTerm) ||
        supplier.address.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const handleExport = () => {
        console.log("Exporting suppliers...")
    }

    // supplier delete api request
    const handleDeleteSupplier = (supplierId: string) => {
        try {
            SupplierApiService.deleteSupplier(Number(supplierId)).then(res => {
                if (res.success) {
                    alert.showSuccess(res.message || "Supplier deleted successfully");
                    refetchSuppliers();
                } else {
                    alert.showError(res.message || "Failed to delete supplier");
                }
            });
        } catch (e) {
            console.error("Supplier Delete Error: ", e);
            alert.showError("Failed to delete supplier");
        }
    }

    // change suppliers status
    const toggleSupplierStatus = async (supplierId: string) => {
        try {
            const result = await SupplierApiService.toggleSupplierStatus(Number(supplierId));
            if (!result.success) {
                alert.showError(result.message || "Failed to update supplier status");
            } else {
                alert.showSuccess(result.message || "Supplier status updated successfully");
                refetchSuppliers();
            }
        } catch (error) {
            alert.showError("Failed to update supplier status");
        }
    }


    const getStatusBadge = (status: number) => {
        return status === 1
            ? <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
            : <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>
    }

    // Calculate summary stats
    const activeSuppliers = suppliers?.filter(s => s.status === 1).length
    const totalPurchaseAmount = suppliers?.reduce((sum, s) => sum + Number(s.totalAmount), 0) || 0
    const avgPurchaseAmount = suppliers && suppliers.length > 0 ? Math.round(totalPurchaseAmount / suppliers.length) : 0

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={() => navigate("/purchases")}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                </Button>
                <div className="flex-1">
                    <h1 className="text-2xl font-bold">Supplier Management</h1>
                    <p className="text-muted-foreground">
                        Manage supplier information and track purchase history.
                    </p>
                </div>
                {/* Add Supplier Dialog */}
                <Dialog open={showAddDialog} onOpenChange={(open) => {
                    if (!open) {
                        setShowAddDialog(false);
                    }
                }}>
                    <DialogTrigger asChild>
                        <Button onClick={() => setShowAddDialog(true)}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Supplier
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>Add New Supplier</DialogTitle>
                            <DialogDescription>
                                Enter supplier details to add them to your database.
                            </DialogDescription>
                        </DialogHeader>
                        <AddSupplier onClose={() => setShowAddDialog(false)} refetchSuppliers={refetchSuppliers} />
                    </DialogContent>
                </Dialog>

                {/* Edit Supplier Dialog */}
                <Dialog open={!!showEditDialog} onOpenChange={(open) => {
                    if (!open) {
                        setEditingSupplierId(null);
                    }
                }}>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>Edit Supplier</DialogTitle>
                            <DialogDescription>
                                Update supplier information.
                            </DialogDescription>
                        </DialogHeader>
                        <EditSupplier
                            supplierid={editingSupplierId}
                            onClose={() => setShowEditDialog(false)}
                            refetchSuppliers={refetchSuppliers}
                        />
                    </DialogContent>
                </Dialog>
            </div>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Suppliers</p>
                                <p className="text-2xl font-bold">{suppliers?.length}</p>
                            </div>
                            <Users className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Active Suppliers</p>
                                <p className="text-2xl font-bold text-green-600">{activeSuppliers}</p>
                            </div>
                            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                                <div className="h-4 w-4 rounded-full bg-green-600"></div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Avg Purchase Value</p>
                                <p className="text-2xl font-bold">₹{avgPurchaseAmount.toLocaleString()}</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search */}
            <Card>
                <CardHeader>
                    <CardTitle>Filters</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search by invoice, supplier, or ID..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        {/* <Select
                            value={statusFilter}
                            onValueChange={setStatusFilter}
                            className="w-[180px]"
                        >
                            <option value="all">All Status</option>
                            <option value="paid">Paid</option>
                            <option value="pending">Pending</option>
                            <option value="partially_paid">Partially Paid</option>
                        </Select> */}
                        {/* <Select
                            value={supplierFilter}
                            onValueChange={setSupplierFilter}
                            className="w-[180px]"
                        >
                            <option value="all">All Suppliers</option>
                            {suppliers.map((supplier) => (
                                <option key={supplier} value={supplier}>
                                    {supplier}
                                </option>
                            ))}
                        </Select> */}
                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={refetchSuppliers} disabled={isLoading}>
                                {isLoading ? (
                                    <>
                                        <Loader className="mr-2" />
                                        Refreshing...
                                    </>
                                ) : (
                                    <>
                                        <RefreshCw className="h-4 w-4 mr-2" />
                                        Refresh
                                    </>
                                )}
                            </Button>
                            <Button variant="outline" size="sm" onClick={handleExport}>
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Suppliers Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Suppliers ({filteredSuppliers?.length}) </CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Supplier</TableHead>
                                <TableHead>Contact</TableHead>
                                <TableHead>GST</TableHead>
                                <TableHead>Purchases</TableHead>
                                <TableHead>Total Amount</TableHead>
                                <TableHead>Last Purchase</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredSuppliers?.map((supplier) => (
                                <TableRow key={supplier.id}>
                                    <TableCell>
                                        <div>
                                            <p className="font-medium">{supplier.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <MapPin className="h-3 w-3" />
                                                {supplier.address.split(',')[0]}
                                            </p>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div>
                                            <p className="flex items-center gap-1">
                                                <Phone className="h-3 w-3" />
                                                {supplier.phone}
                                            </p>
                                            {/* {supplier.email && (
                                                <p className="text-sm text-muted-foreground">{supplier.phone}</p>
                                            )} */}
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        {supplier.gstNumber ? (
                                            <Badge variant="outline" className="font-mono text-xs">
                                                {supplier.gstNumber}
                                            </Badge>
                                        ) : (
                                            <span className="text-muted-foreground">-</span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-1">
                                            <Package className="h-3 w-3" />
                                            {supplier.totalPurchases}
                                        </div>
                                    </TableCell>
                                    <TableCell className="font-medium">
                                        ₹{supplier.totalAmount.toLocaleString()}
                                    </TableCell>
                                    <TableCell>
                                        {supplier.lastPurchase ? (
                                            new Date(supplier.lastPurchase).toLocaleDateString()
                                        ) : (
                                            <span className="text-muted-foreground">Never</span>
                                        )}
                                    </TableCell>
                                    <TableCell>{getStatusBadge(supplier.status)}</TableCell>
                                    <TableCell>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => { setEditingSupplierId(Number(supplier.id)); setShowEditDialog(true) }}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => toggleSupplierStatus(String(supplier.id))}>
                                                    <FileText className="mr-2 h-4 w-4" />
                                                    {supplier.status === 1 ? "Deactivate" : "Activate"}
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem
                                                    onClick={() => handleDeleteSupplier(String(supplier.id))}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>

                    {filteredSuppliers?.length == 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No suppliers found matching your search.</p>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
