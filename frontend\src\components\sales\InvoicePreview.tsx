import { forwardRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Printer, Download, X, CheckCircle } from "lucide-react"
import type { Customer } from "@/data/sampleCustomers"

interface BillItem {
    id: string
    name: string
    quantity: number
    price: number
    gstRate: number
    gstAmount: number
    total: number
    unit: string
    category: string
}

interface BillSummary {
    subtotal: number
    totalGst: number
    grandTotal: number
    itemCount: number
    gstBreakdown: {
        rate: number
        taxableAmount: number
        gstAmount: number
    }[]
}

interface InvoicePreviewProps {
    isOpen: boolean
    onClose: () => void
    billItems: BillItem[]
    customer: Customer | null
    paymentMethod: "Cash" | "UPI" | "Credit"
    billSummary: BillSummary
    invoiceNumber: string
    onCompleteSale?: () => void
}

const InvoicePreview = forwardRef<HTMLDivElement, InvoicePreviewProps>(
    ({ isOpen, onClose, billItems, customer, paymentMethod, billSummary, invoiceNumber, onCompleteSale }, ref) => {
        const currentDate = new Date()
        const invoiceDate = currentDate.toLocaleDateString('en-IN')
        const invoiceTime = currentDate.toLocaleTimeString('en-IN')

        const handlePrint = () => {
            window.print()
        }

        const handleDownload = () => {
            // This would typically generate a PDF
            alert("PDF download feature coming soon!")
        }

        if (!isOpen) return null

        return (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                    {/* Header Controls */}
                    <div className="flex items-center justify-between p-4 border-b bg-gray-50 print:hidden">
                        <h2 className="text-xl font-semibold">Invoice Preview</h2>
                        <div className="flex items-center gap-2">
                            {onCompleteSale && (
                                <Button onClick={onCompleteSale} size="sm">
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Complete Sale
                                </Button>
                            )}
                            <Button onClick={handlePrint} variant="outline" size="sm">
                                <Printer className="h-4 w-4 mr-2" />
                                Print
                            </Button>
                            <Button onClick={handleDownload} variant="outline" size="sm">
                                <Download className="h-4 w-4 mr-2" />
                                Download PDF
                            </Button>
                            <Button onClick={onClose} variant="outline" size="sm">
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Invoice Content */}
                    <div ref={ref} className="p-8 print:p-4">
                        {/* Shop Header */}
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold text-blue-600 mb-2">Kumar General Store</h1>
                            <p className="text-gray-600">Main Market, Sadulpur, Rajasthan - 331023</p>
                            <p className="text-gray-600">Phone: +91 9876543210 | Email: <EMAIL></p>
                            <p className="text-sm text-gray-500 mt-2">GSTIN: 08ABCDE1234F1Z5</p>
                        </div>

                        {/* Invoice Details */}
                        <div className="grid grid-cols-2 gap-8 mb-8">
                            <div>
                                <h3 className="font-semibold text-lg mb-3 text-gray-800">Bill To:</h3>
                                {customer ? (
                                    <div className="space-y-1">
                                        <p className="font-medium text-lg">{customer.name}</p>
                                        <p className="text-gray-600">{customer.phone}</p>
                                        <p className="text-gray-600">{customer.address}</p>
                                        <Badge variant={customer.category === "VIP" ? "default" : customer.category === "Wholesale" ? "secondary" : "outline"}>
                                            {customer.category} Customer
                                        </Badge>
                                    </div>
                                ) : (
                                    <div className="space-y-1">
                                        <p className="font-medium text-lg">Walk-in Customer</p>
                                        <p className="text-gray-600">Cash/UPI Sale</p>
                                    </div>
                                )}
                            </div>
                            <div className="text-right">
                                <h3 className="font-semibold text-lg mb-3 text-gray-800">Invoice Details:</h3>
                                <div className="space-y-1">
                                    <p><span className="font-medium">Invoice #:</span> {invoiceNumber}</p>
                                    <p><span className="font-medium">Date:</span> {invoiceDate}</p>
                                    <p><span className="font-medium">Time:</span> {invoiceTime}</p>
                                    <p><span className="font-medium">Payment:</span> 
                                        <Badge variant={paymentMethod === "Credit" ? "destructive" : paymentMethod === "UPI" ? "default" : "secondary"} className="ml-2">
                                            {paymentMethod}
                                        </Badge>
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Items Table */}
                        <div className="mb-8">
                            <h3 className="font-semibold text-lg mb-4 text-gray-800">Items:</h3>
                            <div className="border rounded-lg overflow-hidden">
                                <table className="w-full">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="text-left p-3 font-medium">Item</th>
                                            <th className="text-center p-3 font-medium">Qty</th>
                                            <th className="text-right p-3 font-medium">Rate</th>
                                            <th className="text-center p-3 font-medium">GST%</th>
                                            <th className="text-right p-3 font-medium">GST Amt</th>
                                            <th className="text-right p-3 font-medium">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {billItems.map((item, index) => (
                                            <tr key={item.id} className={index % 2 === 0 ? "bg-white" : "bg-gray-25"}>
                                                <td className="p-3">
                                                    <div>
                                                        <p className="font-medium">{item.name}</p>
                                                        <p className="text-sm text-gray-500">{item.category}</p>
                                                    </div>
                                                </td>
                                                <td className="text-center p-3">{item.quantity} {item.unit}</td>
                                                <td className="text-right p-3">₹{item.price.toFixed(2)}</td>
                                                <td className="text-center p-3">{item.gstRate}%</td>
                                                <td className="text-right p-3">₹{item.gstAmount.toFixed(2)}</td>
                                                <td className="text-right p-3 font-medium">₹{item.total.toFixed(2)}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* GST Breakdown */}
                        {billSummary.gstBreakdown.length > 0 && (
                            <div className="mb-8">
                                <h3 className="font-semibold text-lg mb-4 text-gray-800">GST Breakdown:</h3>
                                <div className="border rounded-lg overflow-hidden">
                                    <table className="w-full">
                                        <thead className="bg-blue-50">
                                            <tr>
                                                <th className="text-left p-3 font-medium">GST Rate</th>
                                                <th className="text-right p-3 font-medium">Taxable Amount</th>
                                                <th className="text-right p-3 font-medium">CGST</th>
                                                <th className="text-right p-3 font-medium">SGST</th>
                                                <th className="text-right p-3 font-medium">Total GST</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {billSummary.gstBreakdown.map((gst, index) => (
                                                <tr key={gst.rate} className={index % 2 === 0 ? "bg-white" : "bg-gray-25"}>
                                                    <td className="p-3 font-medium">{gst.rate}%</td>
                                                    <td className="text-right p-3">₹{gst.taxableAmount.toFixed(2)}</td>
                                                    <td className="text-right p-3">₹{(gst.gstAmount / 2).toFixed(2)}</td>
                                                    <td className="text-right p-3">₹{(gst.gstAmount / 2).toFixed(2)}</td>
                                                    <td className="text-right p-3 font-medium">₹{gst.gstAmount.toFixed(2)}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}

                        {/* Bill Summary */}
                        <div className="border-t-2 border-gray-300 pt-4">
                            <div className="flex justify-end">
                                <div className="w-80">
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span>Subtotal ({billSummary.itemCount} items):</span>
                                            <span>₹{billSummary.subtotal.toFixed(2)}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Total GST:</span>
                                            <span>₹{billSummary.totalGst.toFixed(2)}</span>
                                        </div>
                                        <Separator />
                                        <div className="flex justify-between text-xl font-bold">
                                            <span>Grand Total:</span>
                                            <span>₹{billSummary.grandTotal.toFixed(2)}</span>
                                        </div>
                                        <div className="text-sm text-gray-600 mt-2">
                                            <p>Amount in words: {numberToWords(billSummary.grandTotal)} Rupees Only</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Footer */}
                        <div className="mt-8 pt-4 border-t text-center text-sm text-gray-600">
                            <p className="mb-2">Thank you for shopping with us!</p>
                            <p>For any queries, please contact us at +91 9876543210</p>
                            <p className="mt-4 text-xs">This is a computer generated invoice.</p>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
)

// Helper function to convert number to words (simplified)
function numberToWords(num: number): string {
    if (num === 0) return "Zero"
    
    const ones = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"]
    const teens = ["Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"]
    const tens = ["", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"]
    
    function convertHundreds(n: number): string {
        let result = ""
        
        if (n >= 100) {
            result += ones[Math.floor(n / 100)] + " Hundred "
            n %= 100
        }
        
        if (n >= 20) {
            result += tens[Math.floor(n / 10)] + " "
            n %= 10
        } else if (n >= 10) {
            result += teens[n - 10] + " "
            return result
        }
        
        if (n > 0) {
            result += ones[n] + " "
        }
        
        return result
    }
    
    const crores = Math.floor(num / 10000000)
    const lakhs = Math.floor((num % 10000000) / 100000)
    const thousands = Math.floor((num % 100000) / 1000)
    const hundreds = num % 1000
    
    let result = ""
    
    if (crores > 0) {
        result += convertHundreds(crores) + "Crore "
    }
    if (lakhs > 0) {
        result += convertHundreds(lakhs) + "Lakh "
    }
    if (thousands > 0) {
        result += convertHundreds(thousands) + "Thousand "
    }
    if (hundreds > 0) {
        result += convertHundreds(hundreds)
    }
    
    return result.trim()
}

InvoicePreview.displayName = "InvoicePreview"

export default InvoicePreview
