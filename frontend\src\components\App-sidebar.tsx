import {
    Sidebar,
    <PERSON>barContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
    Home,
    Package,
    ShoppingCart,
    Users,
    BarChart3,
    Settings,
    Store,
    Truck,
    Plus,
    FileText,
    TrendingUp,
    Eye,
    Upload,
    CreditCard,
    Clock,
    DollarSign,
    UserPlus,
    Building,
    PieChart,
    Wrench,
    ChevronRight
} from "lucide-react"
import { Link, useLocation } from "react-router-dom"
import { useState } from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

// Simple navigation structure - easy to understand
const navigationItems = [
    {
        title: "Dashboard",
        url: "/",
        icon: Home,
        type: "single" // Simple single item
    },
    {
        title: "Inventory",
        icon: Package,
        type: "group", // Group with sub-items
        items: [
            { title: "Products", url: "/inventory/products", icon: Package, quickAccess: true },
            { title: "Stock Management", url: "/inventory/stock", icon: TrendingUp },
            { title: "Categories & Units", url: "/inventory/categories", icon: BarChart3 },
            { title: "Import/Export", url: "/inventory/import-export", icon: Upload },
        ]
    },
    {
        title: "Purchases",
        icon: Truck,
        type: "group",
        items: [
            { title: "Add Purchase", url: "/purchases/add", icon: Plus, quickAccess: true },
            { title: "Purchase List", url: "/purchases/list", icon: FileText },
            { title: "Suppliers", url: "/purchases/suppliers", icon: Users },
            { title: "Reports", url: "/purchases/reports", icon: BarChart3 },
        ]
    },
    {
        title: "Sales & Billing",
        icon: ShoppingCart,
        type: "group",
        items: [
            { title: "New Sale/Bill", url: "/sales/new", icon: Plus, quickAccess: true },
            { title: "Sales History", url: "/sales/history", icon: FileText },
            { title: "Daily Summary", url: "/sales/daily", icon: Eye },
            { title: "Sales Reports", url: "/sales/reports", icon: BarChart3 },
        ]
    },
    {
        title: "Customers",
        icon: Users,
        type: "group",
        items: [
            { title: "Customer List", url: "/customers/list", icon: Users },
            { title: "Add Customer", url: "/customers/add", icon: UserPlus },
            { title: "Purchase History", url: "/customers/history", icon: FileText },
        ]
    },
    {
        title: "Udhaar Management",
        icon: CreditCard,
        type: "group",
        items: [
            { title: "Udhaar Overview", url: "/udhaar/overview", icon: Eye },
            { title: "Give Credit", url: "/udhaar/give", icon: Plus },
            { title: "Collect Payment", url: "/udhaar/collect", icon: DollarSign, quickAccess: true },
            { title: "Payment Reminders", url: "/udhaar/reminders", icon: Clock },
        ]
    },
    {
        title: "Reports & Analytics",
        icon: BarChart3,
        type: "group",
        items: [
            { title: "Business Overview", url: "/reports/business", icon: PieChart },
            { title: "Stock Reports", url: "/reports/stock", icon: Package },
            { title: "Sales Analytics", url: "/reports/sales", icon: TrendingUp },
            { title: "Udhaar Reports", url: "/reports/udhaar", icon: CreditCard },
        ]
    },
    {
        title: "Settings",
        icon: Settings,
        type: "group",
        items: [
            { title: "Shop Settings", url: "/settings/shop", icon: Building },
            { title: "Payment Methods", url: "/settings/payments", icon: CreditCard },
            { title: "User Management", url: "/settings/users", icon: Users },
            { title: "System Config", url: "/settings/system", icon: Wrench },
        ]
    },
]

export function AppSidebar() {
    const location = useLocation()
    const [openGroups, setOpenGroups] = useState<string[]>([])

    // Simple function to check if route is active
    const isActiveRoute = (itemUrl: string) => {
        if (itemUrl === "/") {
            return location.pathname === "/"
        }
        return location.pathname.startsWith(itemUrl)
    }

    // Simple function to check if group has active item
    const isGroupActive = (items: any[]) => {
        return items.some(item => isActiveRoute(item.url))
    }

    // Simple function to toggle group open/close
    const toggleGroup = (groupTitle: string) => {
        setOpenGroups(prev =>
            prev.includes(groupTitle)
                ? prev.filter(g => g !== groupTitle)
                : [...prev, groupTitle]
        )
    }

    return (
        <Sidebar>
            <SidebarHeader>
                <div className="flex items-center gap-2 px-4 py-2">
                    <Store className="h-6 w-6" />
                    <span className="font-semibold">Kirana Shop</span>
                </div>
            </SidebarHeader>
            <SidebarContent>
                <SidebarGroup>
                    <SidebarGroupLabel>Navigation</SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {navigationItems.map((item) => (
                                <SidebarMenuItem key={item.title}>
                                    {/* Simple single item */}
                                    {item.type === "single" && (
                                        <SidebarMenuButton
                                            asChild
                                            isActive={isActiveRoute(item.url!)}
                                        >
                                            <Link to={item.url!}>
                                                <item.icon />
                                                <span>{item.title}</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    )}

                                    {/* Group with sub-items */}
                                    {item.type === "group" && (
                                        <Collapsible
                                            open={openGroups.includes(item.title) || isGroupActive(item.items!)}
                                            onOpenChange={() => toggleGroup(item.title)}
                                        >
                                            <CollapsibleTrigger asChild>
                                                <SidebarMenuButton
                                                    className="w-full justify-between"
                                                    isActive={isGroupActive(item.items!)}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <item.icon />
                                                        <span>{item.title}</span>
                                                    </div>
                                                    <ChevronRight className={`h-4 w-4 transition-transform ${
                                                        openGroups.includes(item.title) || isGroupActive(item.items!)
                                                            ? 'rotate-90'
                                                            : ''
                                                    }`} />
                                                </SidebarMenuButton>
                                            </CollapsibleTrigger>
                                            <CollapsibleContent>
                                                <SidebarMenuSub>
                                                    {item.items!.map((subItem) => (
                                                        <SidebarMenuSubItem key={subItem.title}>
                                                            <SidebarMenuSubButton
                                                                asChild
                                                                isActive={isActiveRoute(subItem.url)}
                                                            >
                                                                <Link to={subItem.url}>
                                                                    <subItem.icon />
                                                                    <span>{subItem.title}</span>
                                                                    {/* Show quick access indicator */}
                                                                    {subItem.quickAccess && (
                                                                        <span className="ml-auto text-xs bg-blue-100 text-blue-600 px-1 rounded">
                                                                            Quick
                                                                        </span>
                                                                    )}
                                                                </Link>
                                                            </SidebarMenuSubButton>
                                                        </SidebarMenuSubItem>
                                                    ))}
                                                </SidebarMenuSub>
                                            </CollapsibleContent>
                                        </Collapsible>
                                    )}
                                </SidebarMenuItem>
                            ))}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>
            <SidebarFooter>
                <div className="p-4 text-xs text-muted-foreground">
                    © 2024 Kirana Shop
                </div>
            </SidebarFooter>
        </Sidebar>
    )
}
