import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    <PERSON>umn,
    CreateDateColumn,
    UpdateDateColumn,
    OneToMany,
    Index,
} from "typeorm";
import { Sales } from "./Sales.model";
import { CreditTransaction } from "./CreditTransaction.model";

@Entity("customers")
export class Customer {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "varchar", length: 255 })
    @Index("idx_customer_name")
    name!: string;

    @Column({ type: "varchar", length: 15, unique: true })
    @Index("idx_customer_phone")
    phone!: string;

    @Column({ type: "varchar", length: 255, nullable: true })
    email?: string;

    @Column({ type: "text", nullable: true })
    address?: string;

    @Column({
        type: "enum",
        enum: ["Regular", "Wholesale", "VIP"],
        default: "Regular"
    })
    @Index("idx_customer_category")
    category!: "Regular" | "Wholesale" | "VIP";

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "credit_limit" })
    creditLimit!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "current_credit" })
    currentCredit!: number;

    @Column("decimal", { precision: 12, scale: 2, default: 0.00, name: "total_purchases" })
    totalPurchases!: number;

    @Column({ type: "int", default: 0, name: "total_transactions" })
    totalTransactions!: number;

    @Column({ type: "int", default: 0, name: "loyalty_points" })
    loyaltyPoints!: number;

    @Column({ type: "varchar", length: 15, nullable: true, name: "gst_number" })
    gstNumber?: string;

    @Column({ type: "boolean", default: true, name: "is_active" })
    isActive!: boolean;

    @Column({ type: "datetime", nullable: true, name: "last_purchase_date" })
    lastPurchaseDate?: Date;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    @UpdateDateColumn({
        name: "updated_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    updatedAt!: Date;

    // Relations
    @OneToMany(() => Sales, (sale) => sale.customer)
    sales!: Sales[];

    @OneToMany(() => CreditTransaction, (creditTransaction) => creditTransaction.customer)
    creditTransactions!: CreditTransaction[];

    // Virtual properties for API responses
    get creditUtilization(): number {
        return this.creditLimit > 0 ? (this.currentCredit / this.creditLimit) * 100 : 0;
    }

    get averageOrderValue(): number {
        return this.totalTransactions > 0 ? this.totalPurchases / this.totalTransactions : 0;
    }

    get creditStatus(): "Good" | "Warning" | "Overdue" {
        const utilization = this.creditUtilization;
        if (utilization >= 90) return "Overdue";
        if (utilization >= 70) return "Warning";
        return "Good";
    }
}
