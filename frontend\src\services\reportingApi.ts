import { apiClient, } from '@/lib/api';
import type { ApiResponse } from '@/lib/api';

// Dashboard summary types
export interface DashboardSummary {
    sales: {
        today: number;
        yesterday: number;
        thisMonth: number;
        lastMonth: number;
        growthPercentage: number;
    };
    transactions: {
        today: number;
        yesterday: number;
        thisMonth: number;
        lastMonth: number;
        growthPercentage: number;
    };
    customers: {
        total: number;
        active: number;
        newThisMonth: number;
        overdueCredit: number;
    };
    inventory: {
        totalProducts: number;
        lowStock: number;
        outOfStock: number;
        totalValue: number;
    };
    paymentMethods: Array<{
        method: string;
        amount: number;
        percentage: number;
        transactions: number;
    }>;
    topProducts: Array<{
        productId: number;
        productName: string;
        quantity: number;
        revenue: number;
    }>;
    recentTransactions: Array<{
        id: number;
        invoiceNumber: string;
        customerName?: string;
        amount: number;
        paymentMethod: string;
        timestamp: string;
    }>;
}

export interface MonthlyComparison {
    currentMonth: {
        sales: number;
        transactions: number;
        customers: number;
        averageOrderValue: number;
    };
    previousMonth: {
        sales: number;
        transactions: number;
        customers: number;
        averageOrderValue: number;
    };
    growth: {
        salesGrowth: number;
        transactionGrowth: number;
        customerGrowth: number;
        aovGrowth: number;
    };
    dailyBreakdown: Array<{
        date: string;
        sales: number;
        transactions: number;
    }>;
}

export interface SalesAnalytics {
    totalSales: number;
    totalTransactions: number;
    averageOrderValue: number;
    period: string;
    paymentMethodBreakdown: Array<{
        method: string;
        amount: number;
        percentage: number;
        transactions: number;
    }>;
    gstBreakdown: Array<{
        rate: number;
        taxableAmount: number;
        gstAmount: number;
    }>;
    hourlyBreakdown: Array<{
        hour: number;
        sales: number;
        transactions: number;
    }>;
    dailyTrend: Array<{
        date: string;
        sales: number;
        transactions: number;
    }>;
    topCustomers: Array<{
        customerId: number;
        customerName: string;
        totalPurchases: number;
        totalAmount: number;
    }>;
}

export interface ProductPerformance {
    totalProducts: number;
    totalRevenue: number;
    averagePrice: number;
    topPerformers: Array<{
        productId: number;
        productName: string;
        category: string;
        quantitySold: number;
        revenue: number;
        profitMargin: number;
    }>;
    categoryBreakdown: Array<{
        category: string;
        revenue: number;
        quantity: number;
        products: number;
    }>;
    lowPerformers: Array<{
        productId: number;
        productName: string;
        quantitySold: number;
        revenue: number;
        lastSaleDate?: string;
    }>;
}

export interface CustomerAnalytics {
    totalCustomers: number;
    activeCustomers: number;
    newCustomers: number;
    totalCreditOutstanding: number;
    averagePurchaseValue: number;
    customerSegmentation: Array<{
        category: string;
        count: number;
        percentage: number;
        averagePurchase: number;
    }>;
    topCustomers: Array<{
        customerId: number;
        customerName: string;
        totalPurchases: number;
        totalAmount: number;
        lastPurchaseDate: string;
    }>;
    creditAnalysis: Array<{
        customerId: number;
        customerName: string;
        creditLimit: number;
        currentCredit: number;
        utilization: number;
        status: string;
    }>;
}

// Reporting API service class
export class ReportingApiService {
    // Get dashboard summary
    static async getDashboardSummary(): Promise<ApiResponse<DashboardSummary>> {
        return apiClient.get<DashboardSummary>('/reports/dashboard-summary');
    }

    // Get monthly comparison report
    static async getMonthlyComparison(params?: {
        currentMonth?: string;
        previousMonth?: string;
    }): Promise<ApiResponse<MonthlyComparison>> {
        return apiClient.get<MonthlyComparison>('/reports/monthly-comparison', params);
    }

    // Get sales analytics
    static async getSalesAnalytics(params?: {
        startDate?: string;
        endDate?: string;
        period?: 'day' | 'week' | 'month' | 'year';
        customerId?: number;
        paymentMethod?: string;
    }): Promise<ApiResponse<SalesAnalytics>> {
        return apiClient.get<SalesAnalytics>('/reports/sales-analytics', params);
    }

    // Get product performance report
    static async getProductPerformance(params?: {
        startDate?: string;
        endDate?: string;
        categoryId?: number;
        limit?: number;
    }): Promise<ApiResponse<ProductPerformance>> {
        return apiClient.get<ProductPerformance>('/reports/product-performance', params);
    }

    // Get customer analytics
    static async getCustomerAnalytics(params?: {
        startDate?: string;
        endDate?: string;
        category?: string;
        creditStatus?: string;
    }): Promise<ApiResponse<CustomerAnalytics>> {
        return apiClient.get<CustomerAnalytics>('/reports/customer-analytics', params);
    }

    // Export reports
    static async exportReport(reportType: string, params?: {
        format?: 'pdf' | 'excel' | 'csv';
        startDate?: string;
        endDate?: string;
        [key: string]: any;
    }): Promise<ApiResponse<{ downloadUrl: string }>> {
        return apiClient.get(`/reports/export/${reportType}`, params);
    }

    // Get custom report data
    static async getCustomReport(params: {
        metrics: string[];
        groupBy?: string;
        filters?: Record<string, any>;
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponse<any>> {
        return apiClient.post('/reports/custom', params);
    }
}

// Export default instance for convenience
export default ReportingApiService;
