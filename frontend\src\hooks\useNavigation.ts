import { useNavigate, useLocation } from "react-router-dom"
import { useCallback } from "react"

// Define valid routes
export const VALID_ROUTES = [
    "/",
    "/dashboard", 
    "/inventory",
    "/sales",
    "/customers",
    "/reports",
    "/settings"
] as const

export type ValidRoute = typeof VALID_ROUTES[number]

export function useNavigation() {
    const navigate = useNavigate()
    const location = useLocation()

    // Safe navigation function that checks if route exists
    const navigateTo = useCallback((path: string) => {
        if (VALID_ROUTES.includes(path as ValidRoute)) {
            navigate(path)
        } else {
            console.warn(`Invalid route: ${path}. Redirecting to dashboard.`)
            navigate("/")
        }
    }, [navigate])

    // Check if current route is valid
    const isValidRoute = useCallback((path?: string) => {
        const currentPath = path || location.pathname
        return VALID_ROUTES.includes(currentPath as ValidRoute)
    }, [location.pathname])

    // Get current route info
    const getCurrentRoute = useCallback(() => {
        return {
            path: location.pathname,
            isValid: isValidRoute(),
            search: location.search,
            hash: location.hash
        }
    }, [location, isValidRoute])

    // Navigate with fallback
    const safeNavigate = useCallback((path: string, fallback: string = "/") => {
        if (isValidRoute(path)) {
            navigate(path)
        } else {
            navigate(fallback)
        }
    }, [navigate, isValidRoute])

    return {
        navigateTo,
        safeNavigate,
        isValidRoute,
        getCurrentRoute,
        validRoutes: VALID_ROUTES
    }
}
