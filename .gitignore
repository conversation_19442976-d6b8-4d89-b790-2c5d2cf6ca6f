# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules/
/backend/node_modules/
/frontend/node_modules/

# Build outputs
/dist/
/dist-ssr/
/build/
*.local

# Environment files
.env
.env.local
.env.*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
/coverage/

# Temporary files
*.tmp
*.temp
