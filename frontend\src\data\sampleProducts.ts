// Sample Indian grocery products for Kirana shop
export interface Product {
    id: string
    name: string
    category: string
    unit: string
    purchasePrice: number
    sellingPrice: number
    gstRate: number
    stock: number
    minStock: number
    barcode?: string
}

export const sampleProducts: Product[] = [
    // Food Grains & Cereals
    { id: "1", name: "Basmati Rice", category: "Food Grains", unit: "kg", purchasePrice: 80, sellingPrice: 95, gstRate: 5, stock: 45, minStock: 10, barcode: "8901030875021" },
    { id: "2", name: "Wheat Flour", category: "Food Grains", unit: "kg", purchasePrice: 35, sellingPrice: 42, gstRate: 5, stock: 60, minStock: 15, barcode: "8901030875022" },
    { id: "3", name: "Rice (Regular)", category: "Food Grains", unit: "kg", purchasePrice: 45, sellingPrice: 55, gstRate: 5, stock: 80, minStock: 20, barcode: "8901030875023" },
    { id: "4", name: "Se<PERSON><PERSON> (Suji)", category: "Food Grains", unit: "kg", purchasePrice: 40, sellingPrice: 48, gstRate: 5, stock: 25, minStock: 5, barcode: "8901030875024" },
    
    // Pulses & Lentils
    { id: "5", name: "Toor Dal", category: "Pulses", unit: "kg", purchasePrice: 120, sellingPrice: 140, gstRate: 5, stock: 25, minStock: 5, barcode: "8901030875025" },
    { id: "6", name: "Moong Dal", category: "Pulses", unit: "kg", purchasePrice: 110, sellingPrice: 130, gstRate: 5, stock: 20, minStock: 5, barcode: "8901030875026" },
    { id: "7", name: "Chana Dal", category: "Pulses", unit: "kg", purchasePrice: 90, sellingPrice: 110, gstRate: 5, stock: 30, minStock: 8, barcode: "8901030875027" },
    { id: "8", name: "Masoor Dal", category: "Pulses", unit: "kg", purchasePrice: 85, sellingPrice: 105, gstRate: 5, stock: 15, minStock: 5, barcode: "8901030875028" },
    { id: "9", name: "Urad Dal", category: "Pulses", unit: "kg", purchasePrice: 130, sellingPrice: 150, gstRate: 5, stock: 18, minStock: 5, barcode: "8901030875029" },
    
    // Cooking Oils
    { id: "10", name: "Sunflower Oil", category: "Cooking Oil", unit: "ltr", purchasePrice: 140, sellingPrice: 165, gstRate: 5, stock: 12, minStock: 3, barcode: "8901030875030" },
    { id: "11", name: "Mustard Oil", category: "Cooking Oil", unit: "ltr", purchasePrice: 160, sellingPrice: 185, gstRate: 5, stock: 8, minStock: 2, barcode: "8901030875031" },
    { id: "12", name: "Coconut Oil", category: "Cooking Oil", unit: "ltr", purchasePrice: 180, sellingPrice: 210, gstRate: 5, stock: 6, minStock: 2, barcode: "8901030875032" },
    
    // Spices & Seasonings
    { id: "13", name: "Turmeric Powder", category: "Spices", unit: "gm", purchasePrice: 120, sellingPrice: 145, gstRate: 5, stock: 500, minStock: 100, barcode: "8901030875033" },
    { id: "14", name: "Red Chili Powder", category: "Spices", unit: "gm", purchasePrice: 180, sellingPrice: 210, gstRate: 5, stock: 400, minStock: 100, barcode: "8901030875034" },
    { id: "15", name: "Coriander Powder", category: "Spices", unit: "gm", purchasePrice: 100, sellingPrice: 125, gstRate: 5, stock: 300, minStock: 50, barcode: "8901030875035" },
    { id: "16", name: "Cumin Powder", category: "Spices", unit: "gm", purchasePrice: 200, sellingPrice: 240, gstRate: 5, stock: 250, minStock: 50, barcode: "8901030875036" },
    { id: "17", name: "Garam Masala", category: "Spices", unit: "gm", purchasePrice: 300, sellingPrice: 350, gstRate: 5, stock: 200, minStock: 50, barcode: "8901030875037" },
    
    // Sugar & Salt
    { id: "18", name: "Sugar", category: "Sweeteners", unit: "kg", purchasePrice: 42, sellingPrice: 50, gstRate: 5, stock: 8, minStock: 10, barcode: "8901030875038" },
    { id: "19", name: "Jaggery (Gud)", category: "Sweeteners", unit: "kg", purchasePrice: 60, sellingPrice: 75, gstRate: 5, stock: 15, minStock: 5, barcode: "8901030875039" },
    { id: "20", name: "Salt", category: "Seasonings", unit: "kg", purchasePrice: 18, sellingPrice: 25, gstRate: 5, stock: 40, minStock: 10, barcode: "8901030875040" },
    
    // Tea & Coffee
    { id: "21", name: "Tea Leaves", category: "Beverages", unit: "gm", purchasePrice: 400, sellingPrice: 480, gstRate: 5, stock: 1000, minStock: 200, barcode: "8901030875041" },
    { id: "22", name: "Coffee Powder", category: "Beverages", unit: "gm", purchasePrice: 600, sellingPrice: 720, gstRate: 5, stock: 500, minStock: 100, barcode: "8901030875042" },
    
    // Dairy Products
    { id: "23", name: "Milk Powder", category: "Dairy", unit: "gm", purchasePrice: 350, sellingPrice: 420, gstRate: 5, stock: 800, minStock: 200, barcode: "8901030875043" },
    { id: "24", name: "Ghee", category: "Dairy", unit: "gm", purchasePrice: 450, sellingPrice: 540, gstRate: 5, stock: 1200, minStock: 300, barcode: "8901030875044" },
    
    // Packaged Foods
    { id: "25", name: "Biscuits (Parle-G)", category: "Snacks", unit: "pkt", purchasePrice: 15, sellingPrice: 20, gstRate: 12, stock: 50, minStock: 10, barcode: "8901030875045" },
    { id: "26", name: "Namkeen Mix", category: "Snacks", unit: "gm", purchasePrice: 200, sellingPrice: 250, gstRate: 12, stock: 800, minStock: 200, barcode: "8901030875046" },
    { id: "27", name: "Instant Noodles", category: "Ready to Cook", unit: "pkt", purchasePrice: 12, sellingPrice: 18, gstRate: 12, stock: 60, minStock: 15, barcode: "8901030875047" },
    
    // Personal Care
    { id: "28", name: "Soap Bar", category: "Personal Care", unit: "pcs", purchasePrice: 25, sellingPrice: 35, gstRate: 18, stock: 40, minStock: 10, barcode: "8901030875048" },
    { id: "29", name: "Shampoo Sachet", category: "Personal Care", unit: "pcs", purchasePrice: 8, sellingPrice: 12, gstRate: 18, stock: 100, minStock: 20, barcode: "8901030875049" },
    { id: "30", name: "Toothpaste", category: "Personal Care", unit: "pcs", purchasePrice: 45, sellingPrice: 60, gstRate: 18, stock: 25, minStock: 5, barcode: "8901030875050" },
]
