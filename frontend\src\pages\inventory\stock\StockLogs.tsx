import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
    ArrowLeft,
    Search,
    Filter,
    Download,
    RefreshCw,
    ChevronDown,
    TrendingUp,
    TrendingDown,
    Activity,
    ShoppingCart,
    Package,
    Calendar,
    User,
    FileText
} from "lucide-react"

// Mock data for stock logs
const mockStockLogs = [
    {
        id: 1,
        date: "2024-01-20 14:30",
        productName: "Basmati Rice 1kg",
        productSku: "BR001",
        type: "Stock In",
        changeType: "IN",
        quantity: 20,
        previousStock: 25,
        newStock: 45,
        reason: "Purchase from supplier - Invoice #INV001",
        user: "Admin",
        source: "Purchase"
    },
    {
        id: 2,
        date: "2024-01-20 12:15",
        productName: "Tata Salt 1kg",
        productSku: "TS001",
        type: "Sale",
        changeType: "OUT",
        quantity: -3,
        previousStock: 11,
        newStock: 8,
        reason: "Customer purchase - Bill #B001",
        user: "Cashier",
        source: "Sale"
    },
    {
        id: 3,
        date: "2024-01-19 16:45",
        productName: "Amul Milk 500ml",
        productSku: "AM001",
        type: "Sale",
        changeType: "OUT",
        quantity: -5,
        previousStock: 5,
        newStock: 0,
        reason: "Customer purchase - Bill #B002",
        user: "Cashier",
        source: "Sale"
    },
    {
        id: 4,
        date: "2024-01-19 10:20",
        productName: "Maggi Noodles",
        productSku: "MN001",
        type: "Adjustment",
        changeType: "ADJUST",
        quantity: -3,
        previousStock: 70,
        newStock: 67,
        reason: "Damaged goods - expired items removed",
        user: "Manager",
        source: "Adjustment"
    },
    {
        id: 5,
        date: "2024-01-18 09:30",
        productName: "Britannia Biscuits",
        productSku: "BB001",
        type: "Stock In",
        changeType: "IN",
        quantity: 12,
        previousStock: 0,
        newStock: 12,
        reason: "Purchase from supplier - Invoice #INV002",
        user: "Admin",
        source: "Purchase"
    },
    {
        id: 6,
        date: "2024-01-17 15:10",
        productName: "Basmati Rice 1kg",
        productSku: "BR001",
        type: "Sale",
        changeType: "OUT",
        quantity: -7,
        previousStock: 32,
        newStock: 25,
        reason: "Customer purchase - Bill #B003",
        user: "Cashier",
        source: "Sale"
    },
    {
        id: 7,
        date: "2024-01-16 11:00",
        productName: "Tata Salt 1kg",
        productSku: "TS001",
        type: "Stock In",
        changeType: "IN",
        quantity: 25,
        previousStock: 0,
        newStock: 25,
        reason: "Initial stock - Opening inventory",
        user: "Admin",
        source: "Opening"
    },
    {
        id: 8,
        date: "2024-01-15 14:20",
        productName: "Basmati Rice 1kg",
        productSku: "BR001",
        type: "Stock In",
        changeType: "IN",
        quantity: 32,
        previousStock: 0,
        newStock: 32,
        reason: "Initial stock - Opening inventory",
        user: "Admin",
        source: "Opening"
    }
]

export default function StockLogs() {
    const navigate = useNavigate()
    const [logs, setLogs] = useState(mockStockLogs)
    const [searchTerm, setSearchTerm] = useState("")
    const [filterType, setFilterType] = useState<string>("all")
    const [filterSource, setFilterSource] = useState<string>("all")
    const [isLoading, setIsLoading] = useState(false)

    // Filter logs based on search and filters
    const filteredLogs = logs.filter(log => {
        const matchesSearch = log.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.productSku.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user.toLowerCase().includes(searchTerm.toLowerCase())
        
        const matchesType = filterType === "all" || log.changeType === filterType
        const matchesSource = filterSource === "all" || log.source === filterSource
        
        return matchesSearch && matchesType && matchesSource
    })

    const handleRefresh = async () => {
        setIsLoading(true)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setIsLoading(false)
    }

    const handleExport = () => {
        console.log("Exporting stock logs...")
        // TODO: Implement export functionality
    }

    const getChangeIcon = (changeType: string) => {
        switch (changeType) {
            case "IN":
                return <TrendingUp className="h-4 w-4 text-green-600" />
            case "OUT":
                return <TrendingDown className="h-4 w-4 text-red-600" />
            case "ADJUST":
                return <Activity className="h-4 w-4 text-orange-600" />
            default:
                return <Activity className="h-4 w-4 text-gray-600" />
        }
    }

    const getChangeBadge = (changeType: string, quantity: number) => {
        switch (changeType) {
            case "IN":
                return (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        +{quantity}
                    </Badge>
                )
            case "OUT":
                return (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                        <TrendingDown className="h-3 w-3 mr-1" />
                        {quantity}
                    </Badge>
                )
            case "ADJUST":
                return (
                    <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
                        <Activity className="h-3 w-3 mr-1" />
                        {quantity > 0 ? '+' : ''}{quantity}
                    </Badge>
                )
            default:
                return <Badge variant="secondary">{quantity}</Badge>
        }
    }

    const getSourceIcon = (source: string) => {
        switch (source) {
            case "Purchase":
                return <Package className="h-4 w-4 text-blue-600" />
            case "Sale":
                return <ShoppingCart className="h-4 w-4 text-green-600" />
            case "Adjustment":
                return <Activity className="h-4 w-4 text-orange-600" />
            case "Opening":
                return <FileText className="h-4 w-4 text-purple-600" />
            default:
                return <Activity className="h-4 w-4 text-gray-600" />
        }
    }

    // Calculate summary stats
    const totalEntries = filteredLogs.length
    const stockInEntries = filteredLogs.filter(log => log.changeType === "IN").length
    const stockOutEntries = filteredLogs.filter(log => log.changeType === "OUT").length
    const adjustmentEntries = filteredLogs.filter(log => log.changeType === "ADJUST").length

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate("/inventory/stock")}
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Stock Management
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Stock Logs & History</h1>
                        <p className="text-muted-foreground">
                            View complete history of all stock movements and changes.
                        </p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleExport}>
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                </div>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Entries</p>
                                <p className="text-2xl font-bold">{totalEntries}</p>
                            </div>
                            <FileText className="h-8 w-8 text-blue-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Stock In</p>
                                <p className="text-2xl font-bold text-green-600">{stockInEntries}</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-green-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Stock Out</p>
                                <p className="text-2xl font-bold text-red-600">{stockOutEntries}</p>
                            </div>
                            <TrendingDown className="h-8 w-8 text-red-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Adjustments</p>
                                <p className="text-2xl font-bold text-orange-600">{adjustmentEntries}</p>
                            </div>
                            <Activity className="h-8 w-8 text-orange-600" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search by product, SKU, reason, or user..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <div className="flex gap-2">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                <Filter className="h-4 w-4 mr-2" />
                                Type
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setFilterType("all")}>
                                All Types
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setFilterType("IN")}>
                                Stock In
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterType("OUT")}>
                                Stock Out
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterType("ADJUST")}>
                                Adjustments
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                Source
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setFilterSource("all")}>
                                All Sources
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setFilterSource("Purchase")}>
                                Purchase
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterSource("Sale")}>
                                Sale
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterSource("Adjustment")}>
                                Adjustment
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterSource("Opening")}>
                                Opening Stock
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Stock Logs Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Stock Movement History ({filteredLogs.length})</span>
                        <div className="flex gap-2">
                            <Badge variant="secondary">
                                Showing {filteredLogs.length} of {logs.length} entries
                            </Badge>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredLogs.length === 0 ? (
                        <div className="text-center py-8">
                            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No stock logs found</h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm || filterType !== "all" || filterSource !== "all"
                                    ? "Try adjusting your search or filter criteria."
                                    : "No stock movements have been recorded yet."
                                }
                            </p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Date & Time</TableHead>
                                        <TableHead>Product</TableHead>
                                        <TableHead>SKU</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Change</TableHead>
                                        <TableHead>Stock</TableHead>
                                        <TableHead>Source</TableHead>
                                        <TableHead>User</TableHead>
                                        <TableHead>Reason</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredLogs.map((log) => (
                                        <TableRow key={log.id}>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm">{log.date}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="font-medium">{log.productName}</div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">{log.productSku}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    {getChangeIcon(log.changeType)}
                                                    <span className="text-sm">{log.type}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {getChangeBadge(log.changeType, log.quantity)}
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">
                                                    {log.previousStock} → <span className="font-medium">{log.newStock}</span>
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    {getSourceIcon(log.source)}
                                                    <span className="text-sm">{log.source}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <User className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm">{log.user}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground text-sm max-w-xs">
                                                <div className="truncate" title={log.reason}>
                                                    {log.reason}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
