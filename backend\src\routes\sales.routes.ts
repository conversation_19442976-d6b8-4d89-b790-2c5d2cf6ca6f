import { Router } from "express";
import { SalesController } from "../controllers/SalesController";

const router = Router();
const salesController = new SalesController();

// Sales CRUD operations
router.post("/", salesController.createSale);
router.get("/", salesController.getSalesHistory);
router.get("/today", salesController.getTodaysSales);
router.get("/analytics", salesController.getSalesAnalytics);
router.get("/date-range", salesController.getSalesByDateRange);
router.get("/daily-summary", salesController.getDailySummary);
router.get("/search-customers", salesController.searchCustomers);
router.get("/:id", salesController.getSaleById);
router.patch("/:id/cancel", salesController.cancelSale);

export default router;
