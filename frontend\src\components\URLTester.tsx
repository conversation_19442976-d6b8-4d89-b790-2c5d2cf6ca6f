import { useState } from "react"
import { <PERSON> } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useNavigation } from "@/hooks/useNavigation"
import { ExternalLink, Check, X } from "lucide-react"

export function URLTester() {
    const [testUrl, setTestUrl] = useState("")
    const { isValidRoute, validRoutes } = useNavigation()

    const handleTestUrl = () => {
        if (testUrl) {
            window.open(testUrl, '_blank')
        }
    }

    return (
        <div className="space-y-6 p-6 border rounded-lg bg-muted/50">
            <div>
                <h3 className="text-lg font-semibold mb-2">URL Testing Tool</h3>
                <p className="text-sm text-muted-foreground">
                    Test different URLs to see how the application handles valid and invalid routes.
                </p>
            </div>

            {/* URL Input */}
            <div className="space-y-2">
                <label className="text-sm font-medium">Test URL:</label>
                <div className="flex gap-2">
                    <Input
                        placeholder="e.g., /invalid-page"
                        value={testUrl}
                        onChange={(e) => setTestUrl(e.target.value)}
                        className="flex-1"
                    />
                    <Button onClick={handleTestUrl} disabled={!testUrl}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Test
                    </Button>
                </div>
                {testUrl && (
                    <div className="flex items-center gap-2 text-sm">
                        {isValidRoute(testUrl) ? (
                            <>
                                <Check className="h-4 w-4 text-green-600" />
                                <span className="text-green-600">Valid route</span>
                            </>
                        ) : (
                            <>
                                <X className="h-4 w-4 text-red-600" />
                                <span className="text-red-600">Invalid route (will show 404)</span>
                            </>
                        )}
                    </div>
                )}
            </div>

            {/* Quick Test Links */}
            <div className="space-y-3">
                <h4 className="text-sm font-medium">Quick Tests:</h4>
                
                <div className="space-y-2">
                    <p className="text-xs text-muted-foreground">Valid URLs:</p>
                    <div className="flex flex-wrap gap-2">
                        {validRoutes.map((route) => (
                            <Button key={route} variant="outline" size="sm" asChild>
                                <Link to={route}>
                                    {route === "/" ? "Dashboard" : route.slice(1)}
                                </Link>
                            </Button>
                        ))}
                    </div>
                </div>

                <div className="space-y-2">
                    <p className="text-xs text-muted-foreground">Invalid URLs (will show 404):</p>
                    <div className="flex flex-wrap gap-2">
                        {["/invalid", "/products", "/admin", "/dashboard/extra"].map((route) => (
                            <Button 
                                key={route} 
                                variant="outline" 
                                size="sm" 
                                onClick={() => window.open(route, '_blank')}
                            >
                                {route}
                            </Button>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}
