import {
    <PERSON><PERSON>ty,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
    JoinColumn,
    Index,
} from "typeorm";
import { Customer } from "./Customer.model";
import { Sales } from "./Sales.model";

@Entity("credit_transactions")
export class CreditTransaction {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Customer, (customer) => customer.creditTransactions, { onDelete: "CASCADE" })
    @JoinColumn({ name: "customer_id" })
    @Index("idx_credit_customer")
    customer!: Customer;

    @ManyToOne(() => Sales, { nullable: true, onDelete: "SET NULL" })
    @JoinColumn({ name: "sale_id" })
    sale?: Sales;

    @Column({
        type: "enum",
        enum: ["Credit_Given", "Payment_Received", "Adjustment"],
        name: "transaction_type"
    })
    @Index("idx_credit_type")
    transactionType!: "Credit_Given" | "Payment_Received" | "Adjustment";

    @Column("decimal", { precision: 10, scale: 2 })
    amount!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "balance_before" })
    balanceBefore!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "balance_after" })
    balanceAfter!: number;

    @Column({ type: "date", nullable: true, name: "due_date" })
    @Index("idx_due_date")
    dueDate?: Date;

    @Column({
        type: "enum",
        enum: ["Cash", "UPI", "Card", "Adjustment"],
        nullable: true,
        name: "payment_method"
    })
    paymentMethod?: "Cash" | "UPI" | "Card" | "Adjustment";

    @Column({ type: "varchar", length: 100, nullable: true, name: "reference_number" })
    referenceNumber?: string;

    @Column({ type: "text", nullable: true })
    description?: string;

    @Column({
        type: "enum",
        enum: ["Active", "Paid", "Overdue", "Written_Off"],
        default: "Active"
    })
    @Index("idx_credit_status")
    status!: "Active" | "Paid" | "Overdue" | "Written_Off";

    @Column({ type: "varchar", length: 100, default: "Admin", name: "created_by" })
    createdBy!: string;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    @UpdateDateColumn({
        name: "updated_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    updatedAt!: Date;

    // Virtual properties for calculations
    get isOverdue(): boolean {
        if (!this.dueDate || this.status === "Paid" || this.status === "Written_Off") {
            return false;
        }
        return new Date() > this.dueDate;
    }

    get daysPastDue(): number {
        if (!this.isOverdue || !this.dueDate) return 0;
        const today = new Date();
        const diffTime = today.getTime() - this.dueDate.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    get transactionTypeDisplay(): string {
        switch (this.transactionType) {
            case "Credit_Given":
                return "Credit Given";
            case "Payment_Received":
                return "Payment Received";
            case "Adjustment":
                return "Balance Adjustment";
            default:
                return this.transactionType;
        }
    }

    get amountDisplay(): string {
        const sign = this.transactionType === "Credit_Given" ? "+" : "-";
        return `${sign}₹${this.amount.toLocaleString('en-IN')}`;
    }
}
