
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useGlobalAlert } from "./GlobalAlert"

function AlertDemo() {
    // Using global context (wrapped in AlertProvider)
    const globalAlert = useGlobalAlert()

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Global Alert Component Demo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Global Context Alert Examples */}
                    <div className="space-y-6">
                        <h3 className="text-lg font-semibold">Global Alert System</h3>
                        <p className="text-muted-foreground">
                            All alerts are managed through the global context and appear in the top-right corner with enhanced animations and progress bars.
                        </p>
                    </div>

                    {/* Interactive Examples with Global Context */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Interactive Global Alerts</h3>

                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => globalAlert.showSuccess("🎉 Product added successfully with enhanced styling!")}
                                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                ✨ Enhanced Success
                            </Button>

                            <Button
                                onClick={() => globalAlert.showWarning("⚠️ Stock is critically low! Immediate action required.")}
                                className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🔔 Enhanced Warning
                            </Button>

                            <Button
                                onClick={() => globalAlert.showError("❌ Critical error occurred! Please contact support immediately.")}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🚨 Enhanced Error
                            </Button>

                            <Button
                                onClick={() => globalAlert.showInfo("💡 Amazing new features are now live! Explore the enhanced inventory system.")}
                                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🌟 Enhanced Info
                            </Button>
                        </div>
                    </div>

                    {/* Additional Global Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Additional Global Alert Examples</h3>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => globalAlert.showSuccess("🌟 Global success with progress bar and enhanced animations!")}
                                variant="outline"
                                className="border-green-600 text-green-600 hover:bg-green-50 hover:border-green-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🎯 Global Success
                            </Button>

                            <Button
                                onClick={() => globalAlert.showWarning("⚡ Global warning with shimmer effects and smooth animations!")}
                                variant="outline"
                                className="border-orange-600 text-orange-600 hover:bg-orange-50 hover:border-orange-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ⚡ Global Warning
                            </Button>

                            <Button
                                onClick={() => globalAlert.showError("🔥 Global error with enhanced styling and hover effects!")}
                                variant="outline"
                                className="border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🔥 Global Error
                            </Button>

                            <Button
                                onClick={() => globalAlert.showInfo("✨ Global info with beautiful gradients and animations!")}
                                variant="outline"
                                className="border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ✨ Global Info
                            </Button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Usage Examples</h3>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Global Context Usage</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// AlertProvider is already wrapped in AppLayout
// Use anywhere in your app
const alert = useGlobalAlert()

// Basic usage
alert.showSuccess("Product saved successfully!")
alert.showError("Failed to save product")
alert.showWarning("Please check your input")
alert.showInfo("New features available")

// All alerts automatically include:
// - Enhanced animations and styling
// - Progress bars with auto-dismiss
// - Fixed top-right positioning
// - Mobile responsive design`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Real-world Examples</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// In form submission
const handleSubmit = async () => {
  try {
    await saveProduct(formData)
    alert.showSuccess("Product added successfully!")
    navigate("/inventory")
  } catch (error) {
    alert.showError("Failed to save product")
  }
}

// In delete operations
const handleDelete = async (id) => {
  try {
    await deleteProduct(id)
    alert.showSuccess("Product deleted!")
  } catch (error) {
    alert.showError("Delete failed")
  }
}`}
                            </pre>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default AlertDemo
