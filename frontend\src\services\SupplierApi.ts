import { apiClient, type ApiResponse } from "@/lib/api";

export interface Supplier {
    id?: number;
    name: string;
    phone: string;
    address: string;
    gstNumber?: string;
    totalPurchases: number;
    totalAmount: number;
    lastPurchase?: string;
    status: number;
    datetime?: string;
}

export class SupplierApiService {

    static async getAllSuppliers(params?: { page?: number; limit?: number; }): Promise<ApiResponse<Supplier[]>> {
        return apiClient.get<Supplier[]>("/suppliers", params);
    }

    static async getSupplierById(id: number): Promise<ApiResponse<Supplier>> {
        return apiClient.get<Supplier>(`/suppliers/${id}`);
    }

    static async addSupplier(supplier: Supplier): Promise<ApiResponse<Supplier>> {
        return apiClient.post<Supplier>("/suppliers", supplier);
    }

    static async updateSupplier(id: number, supplier: Supplier): Promise<ApiResponse<Supplier>> {
        return apiClient.put<Supplier>(`/suppliers/${id}`, supplier);
    }

    static async deleteSupplier(id: number): Promise<ApiResponse<Supplier>> {
        return apiClient.delete<Supplier>(`/suppliers/${id}`);
    }

    static async toggleSupplierStatus(id: number): Promise<ApiResponse<Supplier>> {
        return apiClient.patch<Supplier>(`/suppliers/${id}/status`);
    }

}