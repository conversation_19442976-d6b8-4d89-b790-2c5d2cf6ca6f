
export interface Supplier {
    id: string
    name: string
    phone: string
    email?: string
    address: string
    gstNumber?: string
    totalPurchases: number
    totalAmount: number
    lastPurchase?: string
    status: 0 | 1
}

export const sampleSuppliers: Supplier[] = [
    {
        id: "1",
        name: "ABC Distributors",
        phone: "+91 98765 43210",
        email: "<EMAIL>",
        address: "123 Market Street, Mumbai, Maharashtra 400001",
        gstNumber: "27ABCDE1234F1Z5",
        totalPurchases: 25,
        totalAmount: 125000,
        lastPurchase: "2024-01-15",
        status: 1
    },
    {
        id: "2",
        name: "XYZ Suppliers",
        phone: "+91 87654 32109",
        email: "<EMAIL>",
        address: "456 Trade Center, Delhi, Delhi 110001",
        gstNumber: "27XYZAB5678G2Y4",
        totalPurchases: 18,
        totalAmount: 89000,
        lastPurchase: "2024-01-14",
        status: 1
    },
    {
        id: "3",
        name: "Fresh Foods Ltd",
        phone: "+91 76543 21098",
        address: "789 Food Complex, Bangalore, Karnataka 560001",
        totalPurchases: 12,
        totalAmount: 45000,
        lastPurchase: "2024-01-13",
        status: 1
    },
    {
        id: "4",
        name: "Quality Goods Co",
        phone: "+91 65432 10987",
        email: "<EMAIL>",
        address: "321 Industrial Area, Chennai, Tamil Nadu 600001",
        gstNumber: "33QGCDE9876H3W2",
        totalPurchases: 8,
        totalAmount: 32000,
        lastPurchase: "2024-01-10",
        status: 1
    }
]