import React from "react"
import { cn } from "@/lib/utils"

interface LoaderProps {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "spinner" | "dots" | "pulse" | "bars"
  color?: "primary" | "secondary" | "accent" | "muted"
  text?: string
  className?: string
  overlay?: boolean
  fullScreen?: boolean
}

const Loader: React.FC<LoaderProps> = ({
  size = "md",
  variant = "spinner",
  color = "primary",
  text,
  className,
  overlay = false,
  fullScreen = false
}) => {
  // Size classes
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  }

  // Color classes
  const colorClasses = {
    primary: "text-primary border-primary",
    secondary: "text-secondary border-secondary", 
    accent: "text-accent border-accent",
    muted: "text-muted-foreground border-muted-foreground"
  }

  // Text size classes
  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base", 
    xl: "text-lg"
  }

  // Spinner component
  const Spinner = () => (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-transparent border-t-current",
        sizeClasses[size],
        colorClasses[color],
        className
      )}
    />
  )

  // Dots component
  const Dots = () => (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            "rounded-full animate-pulse",
            size === "sm" ? "w-1 h-1" : size === "md" ? "w-2 h-2" : size === "lg" ? "w-3 h-3" : "w-4 h-4",
            colorClasses[color].split(" ")[0].replace("text-", "bg-")
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: "1s"
          }}
        />
      ))}
    </div>
  )

  // Pulse component
  const Pulse = () => (
    <div
      className={cn(
        "rounded-full animate-pulse",
        sizeClasses[size],
        colorClasses[color].split(" ")[0].replace("text-", "bg-"),
        className
      )}
    />
  )

  // Bars component
  const Bars = () => (
    <div className={cn("flex space-x-1 items-end", className)}>
      {[0, 1, 2, 3].map((i) => (
        <div
          key={i}
          className={cn(
            "animate-pulse rounded-sm",
            size === "sm" ? "w-1" : size === "md" ? "w-1.5" : size === "lg" ? "w-2" : "w-3",
            colorClasses[color].split(" ")[0].replace("text-", "bg-")
          )}
          style={{
            height: size === "sm" ? `${8 + i * 2}px` : size === "md" ? `${12 + i * 3}px` : size === "lg" ? `${16 + i * 4}px` : `${20 + i * 5}px`,
            animationDelay: `${i * 0.1}s`,
            animationDuration: "1.2s"
          }}
        />
      ))}
    </div>
  )

  // Render appropriate variant
  const renderLoader = () => {
    switch (variant) {
      case "dots":
        return <Dots />
      case "pulse":
        return <Pulse />
      case "bars":
        return <Bars />
      default:
        return <Spinner />
    }
  }

  // Loader content
  const loaderContent = (
    <div className="flex flex-col items-center justify-center gap-3">
      {renderLoader()}
      {text && (
        <p className={cn(
          "font-medium",
          textSizeClasses[size],
          colorClasses[color].split(" ")[0]
        )}>
          {text}
        </p>
      )}
    </div>
  )

  // Full screen loader
  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        {loaderContent}
      </div>
    )
  }

  // Overlay loader
  if (overlay) {
    return (
      <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/50 backdrop-blur-sm rounded-lg">
        {loaderContent}
      </div>
    )
  }

  // Inline loader
  return loaderContent
}

// Export different loader variants for convenience
export const SpinnerLoader: React.FC<Omit<LoaderProps, "variant">> = (props) => (
  <Loader {...props} variant="spinner" />
)

export const DotsLoader: React.FC<Omit<LoaderProps, "variant">> = (props) => (
  <Loader {...props} variant="dots" />
)

export const PulseLoader: React.FC<Omit<LoaderProps, "variant">> = (props) => (
  <Loader {...props} variant="pulse" />
)

export const BarsLoader: React.FC<Omit<LoaderProps, "variant">> = (props) => (
  <Loader {...props} variant="bars" />
)

// Table/List specific loader
export const TableLoader: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-3">
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex space-x-4">
        {Array.from({ length: columns }).map((_, j) => (
          <div
            key={j}
            className="h-4 bg-muted animate-pulse rounded"
            style={{ width: `${Math.random() * 40 + 60}%` }}
          />
        ))}
      </div>
    ))}
  </div>
)

// Card loader
export const CardLoader: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="p-6 border rounded-lg space-y-4">
        <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
        <div className="h-8 bg-muted animate-pulse rounded w-1/2" />
        <div className="h-3 bg-muted animate-pulse rounded w-full" />
        <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
      </div>
    ))}
  </div>
)

export default Loader
