import { Construction, Calendar, ArrowRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ComingSoonProps {
    module: string
    expectedPhase?: string
    description?: string
}

export default function ComingSoon({ module, expectedPhase, description }: ComingSoonProps) {
    return (
        <div className="container mx-auto p-6">
            <Card className="max-w-2xl mx-auto">
                <CardHeader className="text-center">
                    <div className="flex justify-center mb-4">
                        <Construction className="h-16 w-16 text-orange-500" />
                    </div>
                    <CardTitle className="text-2xl">{module}</CardTitle>
                    <CardDescription className="text-lg">
                        {description || "This feature is coming soon!"}
                    </CardDescription>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                    {expectedPhase && (
                        <div className="flex items-center justify-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span className="text-sm text-muted-foreground">Expected in:</span>
                            <Badge variant="outline">{expectedPhase}</Badge>
                        </div>
                    )}
                    
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-700">
                            <strong>Good news!</strong> We're building this feature as part of our 
                            comprehensive Kirana Shop Management System. It will include all the 
                            functionality you need to manage your shop efficiently.
                        </p>
                    </div>

                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                        <span>Return to</span>
                        <ArrowRight className="h-4 w-4" />
                        <a href="/" className="text-blue-600 hover:underline">Dashboard</a>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
