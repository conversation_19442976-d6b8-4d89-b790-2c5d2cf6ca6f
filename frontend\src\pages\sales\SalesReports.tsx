import { useState } from "react"
import { useNavigate } from "react-router-dom"
import {
    ArrowLeft,
    Calendar,
    IndianRupee,
    FileText,
    TrendingUp,
    TrendingDown,
    BarChart3,
    PieChart,
    Download,
    Filter,
    Search,
    Users,
    Package,
    CreditCard,
    Eye,
    RefreshCw
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { MetricCard } from "@/components/dashboard/MetricCard"
import { todaysSales, getTodaysSalesTotal, getTodaysSalesCount } from "@/data/sampleTransactions"

export default function SalesReports() {
    const navigate = useNavigate()
    const [dateRange, setDateRange] = useState("today")
    const [reportType, setReportType] = useState("overview")
    const [searchTerm, setSearchTerm] = useState("")

    // Calculate metrics based on selected date range
    const todaysSalesTotal = getTodaysSalesTotal()
    const todaysSalesCount = getTodaysSalesCount()
    const averageOrderValue = todaysSalesCount > 0 ? todaysSalesTotal / todaysSalesCount : 0

    // Calculate weekly/monthly trends (mock data for now)
    const weeklyGrowth = 15.2
    const monthlyGrowth = 8.7
    const yearlyGrowth = 23.4

    // Payment method analysis
    const paymentBreakdown = todaysSales.reduce((acc, sale) => {
        acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.total
        return acc
    }, {} as Record<string, number>)

    // Product category analysis
    const categoryBreakdown = todaysSales.reduce((acc, sale) => {
        sale.items.forEach(item => {
            const category = "General" // Default category for now
            acc[category] = (acc[category] || 0) + item.total
        })
        return acc
    }, {} as Record<string, number>)

    // Top customers by revenue
    const customerRevenue = todaysSales.reduce((acc, sale) => {
        if (sale.customerName) {
            acc[sale.customerName] = (acc[sale.customerName] || 0) + sale.total
        }
        return acc
    }, {} as Record<string, number>)

    const topCustomers = Object.entries(customerRevenue)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)

    // Hourly sales pattern
    const hourlySales = Array.from({ length: 24 }, (_, hour) => {
        const salesInHour = todaysSales.filter(sale => 
            new Date(sale.timestamp).getHours() === hour
        )
        return {
            hour: `${hour}:00`,
            sales: salesInHour.reduce((sum, sale) => sum + sale.total, 0),
            count: salesInHour.length
        }
    }).filter(item => item.sales > 0)

    const getDateRangeLabel = () => {
        switch (dateRange) {
            case "today": return "Today"
            case "week": return "This Week"
            case "month": return "This Month"
            case "year": return "This Year"
            default: return "Today"
        }
    }

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate("/sales")}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Sales
                    </Button>
                    <div>
                        <h1 className="text-2xl sm:text-3xl font-bold">Sales Reports</h1>
                        <p className="text-muted-foreground">
                            Comprehensive sales analytics and insights
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export PDF
                    </Button>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="h-5 w-5" />
                        Report Filters
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Date Range</label>
                            <Select value={dateRange} onValueChange={setDateRange}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="today">Today</SelectItem>
                                    <SelectItem value="week">This Week</SelectItem>
                                    <SelectItem value="month">This Month</SelectItem>
                                    <SelectItem value="year">This Year</SelectItem>
                                    <SelectItem value="custom">Custom Range</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Report Type</label>
                            <Select value={reportType} onValueChange={setReportType}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="overview">Sales Overview</SelectItem>
                                    <SelectItem value="products">Product Analysis</SelectItem>
                                    <SelectItem value="customers">Customer Analysis</SelectItem>
                                    <SelectItem value="payments">Payment Methods</SelectItem>
                                    <SelectItem value="trends">Trends & Patterns</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Search</label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search products, customers..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Period</label>
                            <Badge variant="outline" className="w-full justify-center py-2">
                                <Calendar className="h-3 w-3 mr-1" />
                                {getDateRangeLabel()}
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Key Metrics */}
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    title="Total Revenue"
                    value={`₹${todaysSalesTotal.toLocaleString('en-IN')}`}
                    icon={IndianRupee}
                    trend={{ direction: "up", percentage: weeklyGrowth, label: "vs last period" }}
                />
                <MetricCard
                    title="Total Transactions"
                    value={todaysSalesCount.toString()}
                    icon={FileText}
                    trend={{ direction: "up", percentage: monthlyGrowth, label: "vs last period" }}
                />
                <MetricCard
                    title="Average Order Value"
                    value={`₹${averageOrderValue.toLocaleString('en-IN')}`}
                    icon={TrendingUp}
                    trend={{ direction: "up", percentage: 5.2, label: "vs last period" }}
                />
                <MetricCard
                    title="Growth Rate"
                    value={`${yearlyGrowth}%`}
                    icon={BarChart3}
                    trend={{ direction: "up", percentage: yearlyGrowth, label: "yearly growth" }}
                />
            </div>

            {/* Charts and Analysis */}
            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
                {/* Payment Methods Chart */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <PieChart className="h-5 w-5" />
                            Payment Methods Breakdown
                        </CardTitle>
                        <CardDescription>Revenue distribution by payment type</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {Object.entries(paymentBreakdown).map(([method, amount]) => {
                                const percentage = todaysSalesTotal > 0 ? (amount / todaysSalesTotal) * 100 : 0
                                return (
                                    <div key={method} className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <CreditCard className="h-4 w-4" />
                                                <span className="text-sm font-medium">{method}</span>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">₹{amount.toLocaleString('en-IN')}</p>
                                                <p className="text-xs text-muted-foreground">{percentage.toFixed(1)}%</p>
                                            </div>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div 
                                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${percentage}%` }}
                                            />
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </CardContent>
                </Card>

                {/* Top Customers */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Top Customers
                        </CardTitle>
                        <CardDescription>Highest revenue generating customers</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {topCustomers.length > 0 ? (
                                topCustomers.map(([customer, revenue], index) => (
                                    <div key={customer} className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                                                {index + 1}
                                            </Badge>
                                            <div>
                                                <p className="text-sm font-medium">{customer}</p>
                                                <p className="text-xs text-muted-foreground">Regular Customer</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium">₹{revenue.toLocaleString('en-IN')}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {todaysSalesTotal > 0 ? ((revenue / todaysSalesTotal) * 100).toFixed(1) : 0}%
                                            </p>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-center text-muted-foreground py-4">No customer data available</p>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Hourly Sales Pattern */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Sales Pattern Analysis
                    </CardTitle>
                    <CardDescription>Hourly sales distribution for {getDateRangeLabel().toLowerCase()}</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {hourlySales.length > 0 ? (
                            <div className="grid gap-2">
                                {hourlySales.map((item) => {
                                    const maxSales = Math.max(...hourlySales.map(h => h.sales))
                                    const percentage = maxSales > 0 ? (item.sales / maxSales) * 100 : 0
                                    return (
                                        <div key={item.hour} className="flex items-center gap-4">
                                            <div className="w-16 text-sm font-medium">{item.hour}</div>
                                            <div className="flex-1">
                                                <div className="w-full bg-gray-200 rounded-full h-6 relative">
                                                    <div 
                                                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-6 rounded-full transition-all duration-300 flex items-center justify-end pr-2"
                                                        style={{ width: `${percentage}%` }}
                                                    >
                                                        <span className="text-white text-xs font-medium">
                                                            ₹{item.sales.toLocaleString('en-IN')}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="w-20 text-right">
                                                <p className="text-sm font-medium">{item.count} bills</p>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
                                <p className="text-muted-foreground">No sales pattern data available</p>
                                <p className="text-sm text-muted-foreground mt-2">
                                    Sales data will appear here once transactions are recorded
                                </p>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>Generate specific reports and exports</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <Button variant="outline" className="justify-start">
                            <Download className="h-4 w-4 mr-2" />
                            Export Sales Data
                        </Button>
                        <Button variant="outline" className="justify-start">
                            <FileText className="h-4 w-4 mr-2" />
                            Generate Invoice Report
                        </Button>
                        <Button variant="outline" className="justify-start">
                            <Users className="h-4 w-4 mr-2" />
                            Customer Analysis
                        </Button>
                        <Button variant="outline" className="justify-start" onClick={() => navigate("/sales/daily")}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Daily Summary
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
