import {
    Entity,
    PrimaryGeneratedC<PERSON>umn,
    Column,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    Index,
} from "typeorm";
import { Product } from "./Product.model";

@Entity("stock_movements")
export class StockMovement {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Product, { onDelete: "CASCADE" })
    @JoinColumn({ name: "product_id" })
    @Index("idx_stock_product")
    product!: Product;

    @Column({
        type: "enum",
        enum: ["Sale", "Purchase", "Adjustment", "Return", "Damage"],
        name: "movement_type"
    })
    @Index("idx_stock_type")
    movementType!: "Sale" | "Purchase" | "Adjustment" | "Return" | "Damage";

    @Column({ type: "int", nullable: true, name: "reference_id" })
    @Index("idx_stock_reference")
    referenceId?: number;

    @Column({
        type: "enum",
        enum: ["Sale", "Purchase", "Adjustment"],
        nullable: true,
        name: "reference_type"
    })
    referenceType?: "Sale" | "Purchase" | "Adjustment";

    @Column("decimal", { precision: 8, scale: 3, name: "quantity_before" })
    quantityBefore!: number;

    @Column("decimal", { precision: 8, scale: 3, name: "quantity_changed" })
    quantityChanged!: number; // Positive for in, negative for out

    @Column("decimal", { precision: 8, scale: 3, name: "quantity_after" })
    quantityAfter!: number;

    @Column("decimal", { precision: 10, scale: 2, nullable: true, name: "unit_price" })
    unitPrice?: number;

    @Column("decimal", { precision: 10, scale: 2, nullable: true, name: "total_value" })
    totalValue?: number;

    @Column({ type: "text", nullable: true })
    notes?: string;

    @Column({ type: "varchar", length: 100, default: "Admin", name: "created_by" })
    createdBy!: string;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    @Index("idx_stock_date")
    createdAt!: Date;

    // Virtual properties for calculations
    get isInward(): boolean {
        return this.quantityChanged > 0;
    }

    get isOutward(): boolean {
        return this.quantityChanged < 0;
    }

    get absoluteQuantity(): number {
        return Math.abs(this.quantityChanged);
    }

    get movementDirection(): "In" | "Out" {
        return this.isInward ? "In" : "Out";
    }

    get movementTypeDisplay(): string {
        switch (this.movementType) {
            case "Sale":
                return "Sale (Out)";
            case "Purchase":
                return "Purchase (In)";
            case "Adjustment":
                return this.isInward ? "Adjustment (In)" : "Adjustment (Out)";
            case "Return":
                return "Return (In)";
            case "Damage":
                return "Damage (Out)";
            default:
                return this.movementType;
        }
    }

    get valuePerUnit(): number {
        return this.absoluteQuantity > 0 && this.totalValue 
            ? this.totalValue / this.absoluteQuantity 
            : this.unitPrice || 0;
    }

    // Static methods for creating stock movements
    static createSaleMovement(
        product: Product,
        quantityBefore: number,
        quantitySold: number,
        saleId: number,
        unitPrice: number,
        createdBy: string = "Admin"
    ): Partial<StockMovement> {
        return {
            product,
            movementType: "Sale",
            referenceId: saleId,
            referenceType: "Sale",
            quantityBefore,
            quantityChanged: -quantitySold,
            quantityAfter: quantityBefore - quantitySold,
            unitPrice,
            totalValue: quantitySold * unitPrice,
            createdBy,
            notes: `Stock reduced due to sale #${saleId}`
        };
    }

    static createPurchaseMovement(
        product: Product,
        quantityBefore: number,
        quantityPurchased: number,
        purchaseId: number,
        unitPrice: number,
        createdBy: string = "Admin"
    ): Partial<StockMovement> {
        return {
            product,
            movementType: "Purchase",
            referenceId: purchaseId,
            referenceType: "Purchase",
            quantityBefore,
            quantityChanged: quantityPurchased,
            quantityAfter: quantityBefore + quantityPurchased,
            unitPrice,
            totalValue: quantityPurchased * unitPrice,
            createdBy,
            notes: `Stock added due to purchase #${purchaseId}`
        };
    }

    static createAdjustmentMovement(
        product: Product,
        quantityBefore: number,
        quantityAdjustment: number,
        reason: string,
        createdBy: string = "Admin"
    ): Partial<StockMovement> {
        return {
            product,
            movementType: "Adjustment",
            referenceType: "Adjustment",
            quantityBefore,
            quantityChanged: quantityAdjustment,
            quantityAfter: quantityBefore + quantityAdjustment,
            createdBy,
            notes: `Stock adjustment: ${reason}`
        };
    }
}
