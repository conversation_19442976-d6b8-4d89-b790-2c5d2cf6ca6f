import { useRouteE<PERSON><PERSON>, <PERSON> } from "react-router-dom"
import { AlertTriangle, Home, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/App-sidebar"

export default function ErrorBoundary() {
    const error = useRouteError() as Error

    return (
        <SidebarProvider>
            <AppSidebar />
            <SidebarInset>
                <div className="flex flex-1 flex-col items-center justify-center min-h-screen text-center p-6">
                    <div className="space-y-6 max-w-md">
                        {/* Error Icon */}
                        <div className="flex justify-center">
                            <div className="rounded-full bg-destructive/10 p-4">
                                <AlertTriangle className="h-8 w-8 text-destructive" />
                            </div>
                        </div>

                        {/* Error Message */}
                        <div className="space-y-2">
                            <h1 className="text-2xl font-bold">Oops! Something went wrong</h1>
                            <p className="text-muted-foreground">
                                We encountered an unexpected error. Don't worry, it's not your fault.
                            </p>
                            {error?.message && (
                                <details className="mt-4 text-left">
                                    <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                                        Error Details
                                    </summary>
                                    <pre className="mt-2 text-xs bg-muted p-3 rounded-md overflow-auto">
                                        {error.message}
                                    </pre>
                                </details>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button onClick={() => window.location.reload()}>
                                <RefreshCw className="mr-2 h-4 w-4" />
                                Reload Page
                            </Button>
                            <Button variant="outline" asChild>
                                <Link to="/">
                                    <Home className="mr-2 h-4 w-4" />
                                    Go to Dashboard
                                </Link>
                            </Button>
                        </div>

                        {/* Help Text */}
                        <div className="pt-4">
                            <p className="text-xs text-muted-foreground">
                                If this problem persists, please contact support or try refreshing the page.
                            </p>
                        </div>
                    </div>
                </div>
            </SidebarInset>
        </SidebarProvider>
    )
}
