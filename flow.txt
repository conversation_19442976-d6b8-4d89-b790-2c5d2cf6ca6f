🏪 Kirana Store Project Flow Summary

🔧 Backend Stack

Framework: Express.js (Node.js)

Database: MySQL

ORM: TypeORM

Project Structure: Modular with feature-based routing

✅ Completed Modules

1. 📦 Inventory Management

🎯 Purpose

Track and manage products, their categories, units, and stock levels. Ensure full traceability of stock changes.

🧱 Tables

products: Main product details

categories: Product category definitions

units: Measurement units (Kg, Ltr, etc.)

stock_logs: History of every stock change (in, out, adjust)

🔄 Stock Update Flow

Purchase → stock_logs entry (IN) → Increase product stock

Sale → stock_logs entry (OUT) → Decrease product stock

Manual adjustment → stock_logs entry (ADJUST)

2. 📥 Purchase Management

🎯 Purpose

Handle purchasing process from suppliers and incoming stock addition.

🧱 Tables

purchases: Purchase metadata (supplier, invoice, date, etc.)

purchase_items: Line items in each purchase (product, qty, price)

suppliers: Supplier details (name, phone, address, GST)

🔄 Purchase Flow

Admin selects supplier and enters invoice details.

Enters products bought (via purchase_items).

System:

Adds rows in purchases and purchase_items

Adds IN log in stock_logs

Updates stock in products

📌 Current Project Status

Module

Status

Inventory

✅ Done

Purchase

✅ Done

Billing (Sales)

🔜 Next

Udhaar Management

🔜 Later

Analytics

🔜 Later

🛣 Next Steps

Build TypeORM entities and API routes for completed tables

Begin Billing module (product sale, invoice generation, stock deduction)

This document reflects the current state of the Kirana Store backend system and will be updated as the project expands.

