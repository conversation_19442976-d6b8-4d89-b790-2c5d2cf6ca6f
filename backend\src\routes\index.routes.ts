import { Router } from "express";
import productsRoutes from "./products.routes";
import suppliersRoutes from "./Suppliers.routes";
import helpersRoutes from "./helper.routes";
import salesRoutes from "./sales.routes";
import customerRoutes from "./customer.routes";
import reportingRoutes from "./reporting.routes";

const router = Router();

router.use("/products", productsRoutes);
router.use("/suppliers", suppliersRoutes);
router.use("/helpers", helpersRoutes);
router.use("/sales", salesRoutes);
router.use("/customers", customerRoutes);
router.use("/reports", reportingRoutes);

export default router;