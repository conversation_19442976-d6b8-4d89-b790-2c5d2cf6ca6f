// this is demo product starting 
import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { Category } from "../models/Category.model";
import { CategoryService, UnitService } from "../services/CategoryAndUnitService";

export class CategoryAndUnitController {

    private categoryService: CategoryService;
    private unitService: UnitService;

    constructor() {
        this.categoryService = new CategoryService(AppDataSource);
        this.unitService = new UnitService(AppDataSource);
    }

    async getAllCategories(req: Request, res: Response): Promise<void> {
        try {
            const categories = await this.categoryService.getAllCategories();
            res.json({
                success: true,
                data: categories,
                message: categories?.length ? "Categories fetched successfully" : "No categories found"
            });
        } catch (error) {
            console.error("Error fetching categories:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch categories"
            });
        }
    }

    async getCategoryById(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid category ID. Please provide a valid number."
                });
                return;
            }

            const category = await this.categoryService.getCategoryById(id);
            if (!category) {
                res.json({
                    success: false,
                    message: `Category with ID ${id} not found`
                });
                return;
            }

            res.json({
                success: true,
                data: category,
                message: "Category fetched successfully"
            });
        } catch (error) {
            console.error("Error fetching category:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch category"
            });
        }
    }

    async addCategory(req: Request, res: Response): Promise<void> {
        try {
            if (!req.body.name) {
                res.json({
                    success: false,
                    message: "Category name is required"
                });
                return;
            }

            const newCategory = await this.categoryService.addCategory(req.body);
            res.json({
                success: true,
                data: newCategory,
                message: "Category added successfully"
            });
        } catch (error) {
            console.error("Error adding category:", error);
            const message = error instanceof Error && error.message.includes("duplicate")
                ? "Category name already exists"
                : "Failed to add category";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async updateCategory(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid category ID"
                });
                return;
            }

            if (!Object.keys(req.body).length) {
                res.json({
                    success: false,
                    message: "No update data provided"
                });
                return;
            }

            const category = await this.categoryService.updateCategory(id, req.body);
            if (!category) {
                res.json({
                    success: false,
                    message: "Category not found"
                });
                return;
            }

            res.json({
                success: true,
                data: category,
                message: "Category updated successfully"
            });
        } catch (error) {
            console.error("Error updating category:", error);
            const message = error instanceof Error && error.message.includes("duplicate")
                ? "Category name already exists"
                : "Failed to update category";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async deleteCategory(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid category ID"
                });
                return;
            }

            await this.categoryService.deleteCategory(id);

            res.json({
                success: true,
                message: "Category deleted successfully"
            });
        } catch (error) {
            console.error("Error deleting category:", error);
            const message = error instanceof Error && error.message.includes("foreign key")
                ? "Cannot delete category as it is being used by products"
                : "Failed to delete category";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async toggleCategoryStatus(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid category ID"
                });
                return;
            }

            const category = await this.categoryService.toggleCategoryStatus(id);
            if (!category) {
                res.json({
                    success: false,
                    message: "Category not found"
                });
                return;
            }

            res.json({
                success: true,
                data: category,
                message: `Category status ${category.status ? 'activated' : 'deactivated'} successfully`
            });
        } catch (error) {
            console.error("Error toggling category status:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to toggle category status"
            });
        }
    }

    async getAllUnits(req: Request, res: Response): Promise<void> {
        try {
            const units = await this.unitService.getAllUnits();
            res.json({
                success: true,
                data: units || [],
                message: units?.length ? "Units fetched successfully" : "No units found"
            });
        } catch (error) {
            console.error("Error fetching units:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch units"
            });
        }
    }

    async getUnitById(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid unit ID. Please provide a valid number."
                });
                return;
            }

            const unit = await this.unitService.getUnitById(id);
            if (!unit) {
                res.json({
                    success: false,
                    message: `Unit with ID ${id} not found`
                });
                return;
            }

            res.json({
                success: true,
                data: unit,
                message: "Unit fetched successfully"
            });
        } catch (error) {
            console.error("Error fetching unit:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch unit"
            });
        }
    }

    async addUnit(req: Request, res: Response): Promise<void> {
        try {
            if (!req.body.name) {
                res.json({
                    success: false,
                    message: "Unit name is required"
                });
                return;
            }

            const newUnit = await this.unitService.addUnit(req.body);
            res.json({
                success: true,
                data: newUnit,
                message: "Unit added successfully"
            });
        } catch (error) {
            console.error("Error adding unit:", error);
            const message = error instanceof Error && error.message.includes("duplicate")
                ? "Unit name already exists"
                : "Failed to add unit";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async updateUnit(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid unit ID"
                });
                return;
            }

            if (!Object.keys(req.body).length) {
                res.json({
                    success: false,
                    message: "No update data provided"
                });
                return;
            }

            const unit = await this.unitService.updateUnit(id, req.body);
            if (!unit) {
                res.json({
                    success: false,
                    message: "Unit not found"
                });
                return;
            }

            res.json({
                success: true,
                data: unit,
                message: "Unit updated successfully"
            });
        } catch (error) {
            console.error("Error updating unit:", error);
            const message = error instanceof Error && error.message.includes("duplicate")
                ? "Unit name already exists"
                : "Failed to update unit";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async deleteUnit(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid unit ID"
                });
                return;
            }

            await this.unitService.deleteUnit(id);

            res.json({
                success: true,
                message: "Unit deleted successfully"
            });
        } catch (error) {
            console.error("Error deleting unit:", error);
            const message = error instanceof Error && error.message.includes("foreign key")
                ? "Cannot delete unit as it is being used by products"
                : "Failed to delete unit";
            res.json({
                success: false,
                message: message
            });
        }
    }

    async toggleUnitStatus(req: Request, res: Response): Promise<void> {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                res.json({
                    success: false,
                    message: "Invalid unit ID"
                });
                return;
            }

            const unit = await this.unitService.toggleUnitStatus(id);
            if (!unit) {
                res.json({
                    success: false,
                    message: "Unit not found"
                });
                return;
            }

            res.json({
                success: true,
                data: unit,
                message: `Unit status ${unit.status ? 'activated' : 'deactivated'} successfully`
            });
        } catch (error) {
            console.error("Error toggling unit status:", error);
            res.json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to toggle unit status"
            });
        }
    }
}