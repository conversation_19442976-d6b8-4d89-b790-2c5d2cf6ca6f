import { useState } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Separator } from "@/components/ui/separator"
import {
    ArrowLeft,
    Edit,
    Download,
    Printer,
    Package,
    User,
    Calendar,
    CreditCard,
    FileText,
    Phone,
    MapPin,
    TrendingUp,
    History
} from "lucide-react"

interface PurchaseItem {
    id: string
    productName: string
    unit: string
    quantity: number
    purchasePrice: number
    total: number
    stockBefore: number
    stockAfter: number
}

interface Purchase {
    id: string
    invoiceNumber: string
    supplier: {
        name: string
        phone: string
        address: string
        gst?: string
    }
    date: string
    amount: number
    status: "paid" | "pending" | "partially_paid"
    paymentType: string
    items: PurchaseItem[]
    notes?: string
    createdBy: string
    createdAt: string
    subtotal: number
    tax: number
    discount: number
}

export default function PurchaseDetails() {
    const navigate = useNavigate()
    const { id } = useParams()
    const [isLoading, setIsLoading] = useState(false)

    // Mock data - in real app, this would be fetched based on the ID
    const purchase: Purchase = {
        id: "PUR-001",
        invoiceNumber: "INV-2024-001",
        supplier: {
            name: "ABC Distributors",
            phone: "+91 98765 43210",
            address: "123 Market Street, Mumbai, Maharashtra 400001",
            gst: "27ABCDE1234F1Z5"
        },
        date: "2024-01-15",
        amount: 12500,
        status: "paid",
        paymentType: "cash",
        subtotal: 10593.22,
        tax: 1906.78,
        discount: 0,
        notes: "Regular monthly stock purchase. All items received in good condition.",
        createdBy: "Admin User",
        createdAt: "2024-01-15T10:30:00Z",
        items: [
            {
                id: "1",
                productName: "Basmati Rice 1kg",
                unit: "kg",
                quantity: 50,
                purchasePrice: 120,
                total: 6000,
                stockBefore: 25,
                stockAfter: 75
            },
            {
                id: "2",
                productName: "Tata Salt 1kg",
                unit: "kg",
                quantity: 100,
                purchasePrice: 25,
                total: 2500,
                stockBefore: 15,
                stockAfter: 115
            },
            {
                id: "3",
                productName: "Amul Milk 500ml",
                unit: "piece",
                quantity: 75,
                purchasePrice: 28,
                total: 2100,
                stockBefore: 20,
                stockAfter: 95
            }
        ]
    }

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "paid":
                return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Paid</Badge>
            case "pending":
                return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>
            case "partially_paid":
                return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Partial</Badge>
            default:
                return <Badge variant="secondary">{status}</Badge>
        }
    }

    const getPaymentTypeBadge = (type: string) => {
        const typeMap: { [key: string]: string } = {
            cash: "Cash",
            credit: "Credit",
            bank_transfer: "Bank Transfer",
            cheque: "Cheque"
        }
        return <Badge variant="outline">{typeMap[type] || type}</Badge>
    }

    const handlePrint = () => {
        window.print()
    }

    const handleDownload = () => {
        // Download functionality would be implemented here
        console.log("Downloading purchase details...")
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => navigate("/purchases/list")}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to List
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Purchase Details</h1>
                        <p className="text-muted-foreground">
                            View complete purchase information and stock changes.
                        </p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={handlePrint}>
                        <Printer className="h-4 w-4 mr-2" />
                        Print
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleDownload}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                    </Button>
                    <Button size="sm" onClick={() => navigate(`/purchases/edit/${id}`)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Purchase Info */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Purchase Overview */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Purchase Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Purchase ID</p>
                                    <p className="text-lg font-semibold">{purchase.id}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Invoice Number</p>
                                    <p className="text-lg font-semibold">{purchase.invoiceNumber}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Purchase Date</p>
                                    <p className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4" />
                                        {new Date(purchase.date).toLocaleDateString('en-IN', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        })}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Payment Type</p>
                                    <div className="flex items-center gap-2">
                                        <CreditCard className="h-4 w-4" />
                                        {getPaymentTypeBadge(purchase.paymentType)}
                                    </div>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                                    {getStatusBadge(purchase.status)}
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                                    <p className="text-2xl font-bold text-green-600">₹{purchase.amount.toLocaleString()}</p>
                                </div>
                            </div>
                            
                            {purchase.notes && (
                                <>
                                    <Separator />
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground mb-2">Notes</p>
                                        <p className="text-sm bg-muted p-3 rounded-lg">{purchase.notes}</p>
                                    </div>
                                </>
                            )}
                        </CardContent>
                    </Card>

                    {/* Purchase Items */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Purchase Items ({purchase.items.length})</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Product</TableHead>
                                        <TableHead>Unit</TableHead>
                                        <TableHead>Qty</TableHead>
                                        <TableHead>Price</TableHead>
                                        <TableHead>Total</TableHead>
                                        <TableHead>Stock Change</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {purchase.items.map((item) => (
                                        <TableRow key={item.id}>
                                            <TableCell className="font-medium">{item.productName}</TableCell>
                                            <TableCell>{item.unit}</TableCell>
                                            <TableCell>{item.quantity}</TableCell>
                                            <TableCell>₹{item.purchasePrice.toFixed(2)}</TableCell>
                                            <TableCell>₹{item.total.toFixed(2)}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Badge variant="outline">{item.stockBefore}</Badge>
                                                    <TrendingUp className="h-3 w-3 text-green-600" />
                                                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                                                        {item.stockAfter}
                                                    </Badge>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Supplier Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Supplier Details
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="font-semibold text-lg">{purchase.supplier.name}</p>
                                <div className="flex items-center gap-2 text-muted-foreground mt-1">
                                    <Phone className="h-4 w-4" />
                                    <span>{purchase.supplier.phone}</span>
                                </div>
                            </div>
                            
                            <div>
                                <p className="text-sm font-medium text-muted-foreground mb-1">Address</p>
                                <div className="flex items-start gap-2 text-sm">
                                    <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                                    <span>{purchase.supplier.address}</span>
                                </div>
                            </div>
                            
                            {purchase.supplier.gst && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground mb-1">GST Number</p>
                                    <p className="text-sm font-mono bg-muted p-2 rounded">
                                        {purchase.supplier.gst}
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Purchase Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Purchase Summary</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex justify-between">
                                <span>Subtotal:</span>
                                <span>₹{purchase.subtotal.toFixed(2)}</span>
                            </div>
                            {purchase.discount > 0 && (
                                <div className="flex justify-between text-green-600">
                                    <span>Discount:</span>
                                    <span>-₹{purchase.discount.toFixed(2)}</span>
                                </div>
                            )}
                            <div className="flex justify-between">
                                <span>Tax (GST):</span>
                                <span>₹{purchase.tax.toFixed(2)}</span>
                            </div>
                            <Separator />
                            <div className="flex justify-between font-bold text-lg">
                                <span>Total:</span>
                                <span>₹{purchase.amount.toFixed(2)}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Audit Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <History className="h-5 w-5" />
                                Audit Trail
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Created By</p>
                                <p className="text-sm">{purchase.createdBy}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Created At</p>
                                <p className="text-sm">
                                    {new Date(purchase.createdAt).toLocaleString('en-IN')}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
