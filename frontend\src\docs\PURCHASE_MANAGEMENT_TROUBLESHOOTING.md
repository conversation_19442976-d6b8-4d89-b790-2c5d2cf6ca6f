# Purchase Management - Troubleshooting Guide

## 🚨 **Common Issues and Solutions**

### **Issue 1: "Page Not Found" when clicking Purchase menu**

**Symptoms**: 
- Clicking "Purchases" in sidebar shows 404/Not Found page
- Purchase routes not working

**Causes**:
- Missing route configuration
- Component import errors
- Lazy loading issues

**Solutions**:
1. **Check Route Configuration** (`frontend/src/routes/Index-Routes.tsx`):
   ```tsx
   // Ensure these routes exist:
   {
       path: "purchases",
       element: <Purchases />,
   },
   {
       path: "purchases/add",
       element: <AddPurchase />,
   },
   // ... other purchase routes
   ```

2. **Verify Component Imports**:
   ```tsx
   // Check these imports at top of Index-Routes.tsx:
   const Purchases = lazy(() => import("@/pages/purchases/Purchases"));
   const AddPurchase = lazy(() => import("@/pages/purchases/AddPurchase"));
   // ... other imports
   ```

3. **Check File Structure**:
   ```
   frontend/src/pages/purchases/
   ├── Purchases.tsx
   ├── AddPurchase.tsx
   ├── EditPurchase.tsx
   ├── PurchaseDetails.tsx
   ├── PurchaseList.tsx
   ├── suppliers/
   │   └── SupplierManagement.tsx
   └── reports/
       └── PurchaseReports.tsx
   ```

---

### **Issue 2: Select Components Not Working**

**Symptoms**:
- Dropdowns don't open
- Select components show errors
- Form submissions fail

**Cause**: Incompatible Select component API

**Solution**: Use the simplified Select API:
```tsx
// ❌ Don't use this complex API:
<Select value={value} onValueChange={setValue}>
    <SelectTrigger>
        <SelectValue placeholder="Select..." />
    </SelectTrigger>
    <SelectContent>
        <SelectItem value="option1">Option 1</SelectItem>
    </SelectContent>
</Select>

// ✅ Use this simple API instead:
<Select value={value} onValueChange={setValue} placeholder="Select...">
    <option value="">Select...</option>
    <option value="option1">Option 1</option>
</Select>
```

---

### **Issue 3: Component Import Errors**

**Symptoms**:
- TypeScript errors about missing components
- Build failures
- Runtime errors

**Solutions**:
1. **Check UI Component Imports**:
   ```tsx
   // Ensure these components exist:
   import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
   import { Button } from "@/components/ui/button"
   import { Input } from "@/components/ui/input"
   import { Select } from "@/components/ui/select"
   import { Badge } from "@/components/ui/badge"
   import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
   ```

2. **Verify Icon Imports**:
   ```tsx
   import {
       Truck,
       Plus,
       FileText,
       Users,
       // ... other icons
   } from "lucide-react"
   ```

---

### **Issue 4: Styling Issues**

**Symptoms**:
- Components look different from inventory module
- Inconsistent spacing or colors
- Missing styles

**Solutions**:
1. **Check Tailwind Classes**: Ensure all components use consistent classes
2. **Verify CSS Variables**: Check that design tokens are properly defined
3. **Component Structure**: Follow the same patterns as inventory components

---

### **Issue 5: Navigation Issues**

**Symptoms**:
- Sidebar doesn't show "Purchases" menu
- Navigation links don't work

**Solutions**:
1. **Check Sidebar Configuration** (`frontend/src/components/App-sidebar.tsx`):
   ```tsx
   import { Truck } from "lucide-react" // Add truck icon import
   
   const items = [
       // ... other items
       {
           title: "Purchases",
           url: "/purchases",
           icon: Truck,
       },
       // ... other items
   ]
   ```

2. **Verify useNavigate Usage**:
   ```tsx
   const navigate = useNavigate()
   
   // Use navigate function for routing:
   onClick={() => navigate("/purchases/add")}
   ```

---

## 🔧 **Development Tips**

### **Testing New Components**
1. Start with a simple test component
2. Verify routing works
3. Add complexity gradually
4. Test each feature individually

### **Debugging Steps**
1. Check browser console for errors
2. Verify dev server terminal output
3. Test individual component imports
4. Use React Developer Tools

### **Best Practices**
1. Follow existing component patterns
2. Use consistent naming conventions
3. Keep components modular and reusable
4. Test on different screen sizes

---

## 📞 **Getting Help**

If you encounter issues not covered here:

1. **Check Documentation**: Review the main Purchase Management UI documentation
2. **Console Logs**: Check browser console and dev server terminal
3. **Component Structure**: Compare with working inventory components
4. **Incremental Testing**: Test components one by one

---

## ✅ **Verification Checklist**

After making changes, verify:

- [ ] All purchase routes load without errors
- [ ] Sidebar navigation works
- [ ] Forms submit properly
- [ ] Select components function correctly
- [ ] Styling is consistent with project theme
- [ ] No console errors
- [ ] All pages are responsive

---

**Last Updated**: January 2024  
**Status**: All known issues resolved ✅
