import { useState, type ChangeEvent, type FormEvent, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { User, Phone, MapPin, Building } from "lucide-react";
import { SupplierApiService, type Supplier } from "@/services/SupplierApi";
import { useGlobalAlert } from "@/components/common/GlobalAlert";
import { useApi } from "@/hooks/useApi";


export default function EditSupplier({ onClose, refetchSuppliers, supplierid }: { onClose: () => void } & { refetchSuppliers: () => void } & { supplierid: number | null }) {
  const alert = useGlobalAlert();
  const [newSupplier, setNewSupplier] = useState<Supplier>({} as Supplier);
  const [errors, setErrors] = useState<Partial<Supplier>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: supplier } = useApi(() => SupplierApiService.getSupplierById(supplierid || 0));

  useEffect(() => {
    if (supplier) {
      setNewSupplier(supplier);
    }
  }, [supplier]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setNewSupplier(prev => ({
      ...prev,
      [id]: value
    }));
    if (errors[id as keyof Supplier]) {
      setErrors(prev => ({ ...prev, [id]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Supplier> = {};

    if (!newSupplier.name.trim()) newErrors.name = "Name is required";
    if (!newSupplier.phone.trim()) newErrors.phone = "Phone Number is required";
    if (!newSupplier.address.trim()) newErrors.address = "Address is required";

    if (newSupplier.phone.trim() && !/^\d{10}$/.test(newSupplier.phone)) {
      newErrors.phone = "Please enter a valid 10-digit phone number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateSupplier = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !supplierid) return;

    setIsSubmitting(true);
    try {
      const result = await SupplierApiService.updateSupplier(supplierid, newSupplier);
      if (!result.success) {
        alert.showError(result.message || "Failed to update supplier");
      } else {
        alert.showSuccess(result.message || "Supplier updated successfully");
        onClose();
      }
    } catch (error) {
      console.error("Error updating supplier:", error);
      alert.showError("An error occurred while updating the supplier");
    } finally {
      setIsSubmitting(false);
      refetchSuppliers();
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <form onSubmit={handleUpdateSupplier} className="space-y-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Supplier Name <span className="text-red-600">*</span>
            </Label>
            <Input
              id="name"
              placeholder="Enter supplier name"
              value={newSupplier.name}
              onChange={handleInputChange}
              className={`transition-all ${errors.name ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Phone Number <span className="text-red-600">*</span>
            </Label>
            <Input
              id="phone"
              placeholder="10-digit phone number"
              value={newSupplier.phone}
              onChange={handleInputChange}
              className={`transition-all ${errors.phone ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
            />
            {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="gstNumber" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              GST Number
            </Label>
            <Input
              id="gstNumber"
              placeholder="Enter GST number"
              value={newSupplier.gstNumber}
              onChange={handleInputChange}
              className="transition-all hover:border-primary/50"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="address" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Address <span className="text-red-600">*</span>
          </Label>
          <Textarea
            id="address"
            placeholder="Enter complete address"
            value={newSupplier.address}
            onChange={handleInputChange}
            className={`transition-all min-h-[100px] ${errors.address ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
          />
          {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
        </div>

        <div className="flex justify-end gap-3 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="w-24"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="w-24"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Updating..." : "Update"}
          </Button>
        </div>
      </form>
    </div>
  );
}
