import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { Category } from "./Category.model";
import { Units } from "./Units.model";
import { Suppliers } from "./Suppliers.model";

@Entity("products")
export class Product {
  @PrimaryGeneratedColumn()
  id!: number;

  @Index("idx_product_name")
  @Column({ type: "varchar", length: 255 })
  name!: string;

  @ManyToOne(() => Category, { nullable: false })
  @JoinColumn({ name: "category_id" })
  @Index("product_categories_in_products_fk_idx")
  category!: Category;

  @Column({ type: "varchar", length: 100, nullable: true })
  brand!: string;

  @Column({ type: "text", nullable: true })
  imageUrl!: string;

  @Column({ type: "text", nullable: true })
  desc!: string;

  @Column("decimal", { precision: 10, scale: 2, nullable: true })
  purchasePrice!: number;

  @Column("decimal", { precision: 10, scale: 2, nullable: true })
  sellingPrice!: number;

  @Column({ type: "varchar", length: 50, nullable: true })
  barcode!: string;

  @ManyToOne(() => Units, { nullable: false })
  @JoinColumn({ name: "unit_id" })
  @Index("units_in_products_fk_idx")
  unit!: Units;

  @ManyToOne(() => Suppliers, { nullable: false })
  @JoinColumn({ name: "supplier_id" })
  @Index("supplier_in_products_fk_idx")
  supplier!: Suppliers;

  @Column({ type: "int", default: 0, nullable: true })
  stock!: number;

  @Column({
    type: "enum",
    enum: ["out", "in", "low"],
    default: "out"
  })
  status!: "out" | "in" | "low";

  @Column({ type: "int", default: 5, nullable: true })
  minStock!: number;

  @CreateDateColumn({
    name: "created_at",
    type: "datetime",
    default: () => "CURRENT_TIMESTAMP"
  })
  createdAt!: Date;

  @UpdateDateColumn({
    name: "updated_at",
    type: "datetime",
    default: () => "CURRENT_TIMESTAMP"
  })
  updatedAt!: Date;
}
