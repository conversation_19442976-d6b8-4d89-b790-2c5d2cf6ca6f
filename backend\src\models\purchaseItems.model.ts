import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    <PERSON><PERSON><PERSON><PERSON><PERSON>n,
    CreateDateColumn,
} from "typeorm";
import { Purchase } from "./Purchase.model";
import { Product } from "./Product.model";


@Entity("purchase_items")
export class PurchaseItem {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Purchase)
    @JoinColumn({ name: "purchase_id" })
    purchase!: Purchase;

    @ManyToOne(() => Product)
    @JoinColumn({ name: "products_id" })
    product!: Product;

    @Column()
    quantity!: number;

    @Column("decimal", { precision: 10, scale: 2 })
    price!: number;

    @Column("decimal", { precision: 10, scale: 2 })
    totalAmount!: number;

    @CreateDateColumn({ type: "datetime", name: "datetime", default: () => "CURRENT_TIMESTAMP" })
    datetime!: Date;
}
