import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    Package,
    Plus,
    Minus,
    Search,
    Filter,
    TrendingUp,
    TrendingDown,
    AlertTriangle,
    Eye,
    Edit,
    MoreHorizontal,
    ChevronDown,
    RefreshCw,
    History,
    <PERSON>tings,
    CheckCircle,
    XCircle
} from "lucide-react"

// Mock data for stock management
const mockStockData = [
    {
        id: 1,
        name: "Basmati Rice 1kg",
        sku: "BR001",
        category: "Grains",
        currentStock: 45,
        lowStockThreshold: 10,
        unit: "kg",
        price: 120,
        lastUpdated: "2 hours ago",
        status: "In Stock"
    },
    {
        id: 2,
        name: "Tata Salt 1kg",
        sku: "TS001",
        category: "Spices",
        currentStock: 8,
        lowStockThreshold: 15,
        unit: "kg",
        price: 25,
        lastUpdated: "4 hours ago",
        status: "Low Stock"
    },
    {
        id: 3,
        name: "Amul Milk 500ml",
        sku: "AM001",
        category: "Dairy",
        currentStock: 0,
        lowStockThreshold: 20,
        unit: "ml",
        price: 28,
        lastUpdated: "1 day ago",
        status: "Out of Stock"
    },
    {
        id: 4,
        name: "Maggi Noodles",
        sku: "MN001",
        category: "Instant Food",
        currentStock: 67,
        lowStockThreshold: 25,
        unit: "pack",
        price: 15,
        lastUpdated: "3 hours ago",
        status: "In Stock"
    },
    {
        id: 5,
        name: "Britannia Biscuits",
        sku: "BB001",
        category: "Snacks",
        currentStock: 12,
        lowStockThreshold: 15,
        unit: "pack",
        price: 35,
        lastUpdated: "5 hours ago",
        status: "Low Stock"
    }
]

export default function StockManagement() {
    const navigate = useNavigate()
    const [stockData, setStockData] = useState(mockStockData)
    const [searchTerm, setSearchTerm] = useState("")
    const [filterStatus, setFilterStatus] = useState<string>("all")
    const [isLoading, setIsLoading] = useState(false)
    const [adjustDialogOpen, setAdjustDialogOpen] = useState(false)
    const [selectedProduct, setSelectedProduct] = useState<any>(null)
    const [adjustmentType, setAdjustmentType] = useState<"in" | "out">("in")
    const [adjustmentQuantity, setAdjustmentQuantity] = useState("")
    const [adjustmentReason, setAdjustmentReason] = useState("")
    const [showSuccess, setShowSuccess] = useState(false)

    // Filter products based on search and status
    const filteredProducts = stockData.filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.category.toLowerCase().includes(searchTerm.toLowerCase())
        
        const matchesStatus = filterStatus === "all" || 
            (filterStatus === "in-stock" && product.currentStock > product.lowStockThreshold) ||
            (filterStatus === "low-stock" && product.currentStock <= product.lowStockThreshold && product.currentStock > 0) ||
            (filterStatus === "out-of-stock" && product.currentStock === 0)
        
        return matchesSearch && matchesStatus
    })

    // Calculate stats
    const totalProducts = stockData.length
    const lowStockCount = stockData.filter(p => p.currentStock <= p.lowStockThreshold && p.currentStock > 0).length
    const outOfStockCount = stockData.filter(p => p.currentStock === 0).length
    const totalValue = stockData.reduce((sum, p) => sum + (p.currentStock * p.price), 0)

    const handleRefresh = async () => {
        setIsLoading(true)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setIsLoading(false)
    }

    const openAdjustDialog = (product: any, type: "in" | "out") => {
        setSelectedProduct(product)
        setAdjustmentType(type)
        setAdjustmentQuantity("")
        setAdjustmentReason("")
        setAdjustDialogOpen(true)
    }

    const handleStockAdjustment = () => {
        if (!selectedProduct || !adjustmentQuantity) return

        const quantity = parseInt(adjustmentQuantity)
        const newStock = adjustmentType === "in" 
            ? selectedProduct.currentStock + quantity
            : Math.max(0, selectedProduct.currentStock - quantity)

        setStockData(stockData.map(product => 
            product.id === selectedProduct.id 
                ? { 
                    ...product, 
                    currentStock: newStock,
                    lastUpdated: "Just now",
                    status: newStock === 0 ? "Out of Stock" : 
                           newStock <= product.lowStockThreshold ? "Low Stock" : "In Stock"
                  }
                : product
        ))

        setAdjustDialogOpen(false)
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const getStockStatus = (product: any) => {
        if (product.currentStock === 0) {
            return { label: "Out of Stock", variant: "destructive" as const, icon: XCircle }
        } else if (product.currentStock <= product.lowStockThreshold) {
            return { label: "Low Stock", variant: "warning" as const, icon: AlertTriangle }
        } else {
            return { label: "In Stock", variant: "success" as const, icon: CheckCircle }
        }
    }

    const getStatusBadge = (product: any) => {
        const status = getStockStatus(product)
        const Icon = status.icon
        
        const variantClasses = {
            destructive: "bg-red-100 text-red-800 hover:bg-red-100",
            warning: "bg-orange-100 text-orange-800 hover:bg-orange-100",
            success: "bg-green-100 text-green-800 hover:bg-green-100"
        }

        return (
            <Badge className={variantClasses[status.variant]}>
                <Icon className="h-3 w-3 mr-1" />
                {status.label}
            </Badge>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Stock Management</h1>
                    <p className="text-muted-foreground">
                        Monitor stock levels, adjust inventory, and manage stock alerts.
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => navigate("/inventory/stock/logs")}>
                        <History className="h-4 w-4 mr-2" />
                        Stock Logs
                    </Button>
                    <Button size="sm" onClick={() => navigate("/inventory/stock/adjust")}>
                        <Settings className="h-4 w-4 mr-2" />
                        Bulk Adjust
                    </Button>
                </div>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                        Stock adjustment completed successfully!
                    </AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                                <p className="text-2xl font-bold">{totalProducts}</p>
                            </div>
                            <Package className="h-8 w-8 text-blue-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Low Stock Items</p>
                                <p className="text-2xl font-bold text-orange-600">{lowStockCount}</p>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-orange-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Out of Stock</p>
                                <p className="text-2xl font-bold text-red-600">{outOfStockCount}</p>
                            </div>
                            <XCircle className="h-8 w-8 text-red-600" />
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                                <p className="text-2xl font-bold text-green-600">₹{totalValue.toLocaleString()}</p>
                            </div>
                            <TrendingUp className="h-8 w-8 text-green-600" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search products by name, SKU, or category..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter Status
                            <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                            All Products
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => setFilterStatus("in-stock")}>
                            In Stock
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setFilterStatus("low-stock")}>
                            Low Stock
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setFilterStatus("out-of-stock")}>
                            Out of Stock
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Stock Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Stock Overview ({filteredProducts.length})</span>
                        <div className="flex gap-2">
                            <Badge variant="secondary">
                                In Stock: {filteredProducts.filter(p => p.currentStock > p.lowStockThreshold).length}
                            </Badge>
                            <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
                                Low: {filteredProducts.filter(p => p.currentStock <= p.lowStockThreshold && p.currentStock > 0).length}
                            </Badge>
                            <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                                Out: {filteredProducts.filter(p => p.currentStock === 0).length}
                            </Badge>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredProducts.length === 0 ? (
                        <div className="text-center py-8">
                            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No products found</h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm || filterStatus !== "all"
                                    ? "Try adjusting your search or filter criteria."
                                    : "No products available for stock management."
                                }
                            </p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Product</TableHead>
                                        <TableHead>SKU</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Current Stock</TableHead>
                                        <TableHead>Low Stock Alert</TableHead>
                                        <TableHead>Unit Price</TableHead>
                                        <TableHead>Total Value</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Last Updated</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredProducts.map((product) => (
                                        <TableRow key={product.id}>
                                            <TableCell>
                                                <div className="font-medium">{product.name}</div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                                            <TableCell className="text-muted-foreground">{product.category}</TableCell>
                                            <TableCell>
                                                <span className={`font-medium ${
                                                    product.currentStock === 0 ? 'text-red-600' :
                                                    product.currentStock <= product.lowStockThreshold ? 'text-orange-600' : 'text-green-600'
                                                }`}>
                                                    {product.currentStock} {product.unit}
                                                </span>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {product.lowStockThreshold} {product.unit}
                                            </TableCell>
                                            <TableCell>₹{product.price}</TableCell>
                                            <TableCell>₹{(product.currentStock * product.price).toLocaleString()}</TableCell>
                                            <TableCell>{getStatusBadge(product)}</TableCell>
                                            <TableCell className="text-muted-foreground text-sm">{product.lastUpdated}</TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => navigate(`/inventory/products/details/${product.id}`)}>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => openAdjustDialog(product, "in")}>
                                                            <Plus className="h-4 w-4 mr-2" />
                                                            Stock In
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => openAdjustDialog(product, "out")}>
                                                            <Minus className="h-4 w-4 mr-2" />
                                                            Stock Out
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem onClick={() => navigate(`/inventory/products/edit/${product.id}`)}>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit Product
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Stock Adjustment Dialog */}
            <Dialog open={adjustDialogOpen} onOpenChange={setAdjustDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {adjustmentType === "in" ? "Stock In" : "Stock Out"} - {selectedProduct?.name}
                        </DialogTitle>
                        <DialogDescription>
                            Current stock: {selectedProduct?.currentStock} {selectedProduct?.unit}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">
                                Quantity to {adjustmentType === "in" ? "add" : "remove"}
                            </label>
                            <Input
                                type="number"
                                value={adjustmentQuantity}
                                onChange={(e) => setAdjustmentQuantity(e.target.value)}
                                placeholder="Enter quantity"
                                min="1"
                            />
                        </div>
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Reason</label>
                            <Input
                                value={adjustmentReason}
                                onChange={(e) => setAdjustmentReason(e.target.value)}
                                placeholder="Enter reason for adjustment"
                            />
                        </div>
                        {adjustmentQuantity && (
                            <div className="p-3 bg-muted rounded-lg">
                                <p className="text-sm">
                                    New stock will be: <span className="font-medium">
                                        {adjustmentType === "in" 
                                            ? selectedProduct?.currentStock + parseInt(adjustmentQuantity)
                                            : Math.max(0, selectedProduct?.currentStock - parseInt(adjustmentQuantity))
                                        } {selectedProduct?.unit}
                                    </span>
                                </p>
                            </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setAdjustDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button 
                            onClick={handleStockAdjustment}
                            disabled={!adjustmentQuantity || parseInt(adjustmentQuantity) <= 0}
                        >
                            {adjustmentType === "in" ? <Plus className="h-4 w-4 mr-2" /> : <Minus className="h-4 w-4 mr-2" />}
                            Confirm {adjustmentType === "in" ? "Stock In" : "Stock Out"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
