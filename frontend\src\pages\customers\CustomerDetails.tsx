import React, { useState, useMemo } from 'react';
import { useP<PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { 
    ArrowLeft, 
    Edit, 
    Phone, 
    Mail, 
    MapPin, 
    CreditCard, 
    TrendingUp, 
    Calendar,
    Building,
    FileText,
    ShoppingCart,
    DollarSign,
    AlertTriangle,
    CheckCircle,
    Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    Table, 
    TableBody, 
    TableCell, 
    TableHead, 
    TableHeader, 
    TableRow 
} from '@/components/ui/table';
import { useApi } from '@/hooks/useApi';
import { customerApi, Customer } from '@/services/customerApi';
import { GlobalLoader } from '@/components/common/GlobalLoader';
import { GlobalAlert } from '@/components/common/GlobalAlert';

// Fallback to sample data for development
import { sampleCustomers } from '@/data/sampleCustomers';
import { sampleSales } from '@/data/sampleSales';

const CustomerDetails: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [alertState, setAlertState] = useState<{
        show: boolean;
        message: string;
        type: 'success' | 'error' | 'warning';
    }>({ show: false, message: '', type: 'success' });

    // API call with fallback to sample data
    const { 
        data: apiCustomer, 
        loading, 
        error 
    } = useApi(() => customerApi.getCustomerById(parseInt(id || '0')));

    // Use API data if available, otherwise fallback to sample data
    const customer = useMemo(() => {
        if (apiCustomer) {
            return apiCustomer;
        }
        
        // Find customer in sample data
        const sampleCustomer = sampleCustomers.find(c => c.id === id);
        if (!sampleCustomer) return null;
        
        // Transform sample data to match API interface
        return {
            ...sampleCustomer,
            id: parseInt(sampleCustomer.id),
            creditUtilization: sampleCustomer.creditLimit > 0 ? (sampleCustomer.currentCredit / sampleCustomer.creditLimit) * 100 : 0,
            creditStatus: (() => {
                const utilization = sampleCustomer.creditLimit > 0 ? (sampleCustomer.currentCredit / sampleCustomer.creditLimit) * 100 : 0;
                if (utilization >= 90) return "Overdue" as const;
                if (utilization >= 70) return "Warning" as const;
                return "Good" as const;
            })(),
            availableCredit: sampleCustomer.creditLimit - sampleCustomer.currentCredit,
            isActive: true,
            loyaltyPoints: Math.floor(sampleCustomer.totalPurchases / 100),
            lastPurchaseDate: sampleCustomer.lastPurchase,
            createdAt: sampleCustomer.joinDate,
            updatedAt: sampleCustomer.joinDate,
            businessName: sampleCustomer.category === 'Wholesale' ? `${sampleCustomer.name} Enterprises` : undefined,
            gstNumber: sampleCustomer.category === 'Wholesale' ? '22AAAAA0000A1Z5' : undefined,
            email: `${sampleCustomer.name.toLowerCase().replace(' ', '.')}@example.com`,
            notes: `Regular customer since ${sampleCustomer.joinDate}`
        };
    }, [apiCustomer, id]);

    // Get customer's recent sales (sample data)
    const customerSales = useMemo(() => {
        return sampleSales.filter(sale => sale.customer?.id === id).slice(0, 5);
    }, [id]);

    const showAlert = (message: string, type: 'success' | 'error' | 'warning') => {
        setAlertState({ show: true, message, type });
        setTimeout(() => setAlertState(prev => ({ ...prev, show: false })), 3000);
    };

    const getCreditStatusColor = (status: string) => {
        switch (status) {
            case 'Good': return 'bg-green-100 text-green-800';
            case 'Warning': return 'bg-yellow-100 text-yellow-800';
            case 'Overdue': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'VIP': return 'bg-purple-100 text-purple-800';
            case 'Wholesale': return 'bg-blue-100 text-blue-800';
            case 'Regular': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) return <GlobalLoader />;

    if (!customer) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => navigate('/customers/list')}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Customers
                    </Button>
                </div>
                <Card>
                    <CardContent className="p-8 text-center">
                        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h2 className="text-xl font-semibold text-gray-900 mb-2">Customer Not Found</h2>
                        <p className="text-gray-600 mb-4">The customer you're looking for doesn't exist or has been removed.</p>
                        <Button onClick={() => navigate('/customers/list')}>
                            Return to Customer List
                        </Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Alert */}
            {alertState.show && (
                <GlobalAlert 
                    message={alertState.message} 
                    type={alertState.type} 
                />
            )}

            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => navigate('/customers/list')}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Customers
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">{customer.name}</h1>
                        <div className="flex items-center gap-2 mt-1">
                            <Badge className={getCategoryColor(customer.category)}>
                                {customer.category}
                            </Badge>
                            <Badge variant={customer.isActive ? "default" : "secondary"}>
                                {customer.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                            <Badge className={getCreditStatusColor(customer.creditStatus)}>
                                {customer.creditStatus}
                            </Badge>
                        </div>
                    </div>
                </div>
                <Link to={`/customers/edit/${customer.id}`}>
                    <Button className="flex items-center gap-2">
                        <Edit className="h-4 w-4" />
                        Edit Customer
                    </Button>
                </Link>
            </div>

            {/* Customer Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Purchases</p>
                                <p className="text-2xl font-bold">₹{customer.totalPurchases.toLocaleString()}</p>
                            </div>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <ShoppingCart className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Current Credit</p>
                                <p className="text-2xl font-bold">₹{customer.currentCredit.toLocaleString()}</p>
                            </div>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <CreditCard className="h-6 w-6 text-orange-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Available Credit</p>
                                <p className="text-2xl font-bold">₹{customer.availableCredit.toLocaleString()}</p>
                            </div>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Loyalty Points</p>
                                <p className="text-2xl font-bold">{customer.loyaltyPoints}</p>
                            </div>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <TrendingUp className="h-6 w-6 text-purple-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Customer Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Customer Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-3">
                            <div className="flex items-center gap-3">
                                <Phone className="h-4 w-4 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-600">Phone Number</p>
                                    <p className="font-medium">{customer.phone}</p>
                                </div>
                            </div>

                            {customer.email && (
                                <div className="flex items-center gap-3">
                                    <Mail className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-600">Email Address</p>
                                        <p className="font-medium">{customer.email}</p>
                                    </div>
                                </div>
                            )}

                            {customer.address && (
                                <div className="flex items-start gap-3">
                                    <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                                    <div>
                                        <p className="text-sm text-gray-600">Address</p>
                                        <p className="font-medium">{customer.address}</p>
                                    </div>
                                </div>
                            )}

                            {customer.businessName && (
                                <div className="flex items-center gap-3">
                                    <Building className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-600">Business Name</p>
                                        <p className="font-medium">{customer.businessName}</p>
                                    </div>
                                </div>
                            )}

                            {customer.gstNumber && (
                                <div className="flex items-center gap-3">
                                    <FileText className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-600">GST Number</p>
                                        <p className="font-medium">{customer.gstNumber}</p>
                                    </div>
                                </div>
                            )}

                            <div className="flex items-center gap-3">
                                <Calendar className="h-4 w-4 text-gray-400" />
                                <div>
                                    <p className="text-sm text-gray-600">Customer Since</p>
                                    <p className="font-medium">
                                        {new Date(customer.createdAt).toLocaleDateString('en-IN', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        })}
                                    </p>
                                </div>
                            </div>

                            {customer.lastPurchaseDate && (
                                <div className="flex items-center gap-3">
                                    <Clock className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm text-gray-600">Last Purchase</p>
                                        <p className="font-medium">
                                            {new Date(customer.lastPurchaseDate).toLocaleDateString('en-IN')}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {customer.notes && (
                            <>
                                <Separator />
                                <div>
                                    <p className="text-sm text-gray-600 mb-2">Notes</p>
                                    <p className="text-sm bg-gray-50 p-3 rounded-lg">{customer.notes}</p>
                                </div>
                            </>
                        )}
                    </CardContent>
                </Card>

                {/* Credit Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Credit Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-4">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Credit Limit</span>
                                <span className="font-medium">₹{customer.creditLimit.toLocaleString()}</span>
                            </div>

                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Current Credit</span>
                                <span className="font-medium">₹{customer.currentCredit.toLocaleString()}</span>
                            </div>

                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600">Available Credit</span>
                                <span className="font-medium text-green-600">₹{customer.availableCredit.toLocaleString()}</span>
                            </div>

                            <Separator />

                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <span className="text-sm text-gray-600">Credit Utilization</span>
                                    <span className="font-medium">{customer.creditUtilization.toFixed(1)}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        className={`h-2 rounded-full ${
                                            customer.creditUtilization >= 90 ? 'bg-red-500' :
                                            customer.creditUtilization >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                                        }`}
                                        style={{ width: `${Math.min(customer.creditUtilization, 100)}%` }}
                                    />
                                </div>
                                <div className="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>0%</span>
                                    <span>100%</span>
                                </div>
                            </div>

                            <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50">
                                {customer.creditStatus === 'Good' && <CheckCircle className="h-4 w-4 text-green-500" />}
                                {customer.creditStatus === 'Warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                                {customer.creditStatus === 'Overdue' && <AlertTriangle className="h-4 w-4 text-red-500" />}
                                <span className="text-sm">
                                    {customer.creditStatus === 'Good' && 'Credit status is healthy'}
                                    {customer.creditStatus === 'Warning' && 'Credit utilization is high'}
                                    {customer.creditStatus === 'Overdue' && 'Credit limit exceeded'}
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Recent Sales */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Recent Sales</span>
                        <Link to={`/customers/history?customer=${customer.id}`}>
                            <Button variant="outline" size="sm">
                                View All Sales
                            </Button>
                        </Link>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {customerSales.length === 0 ? (
                        <div className="text-center py-8">
                            <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500">No recent sales found</p>
                            <p className="text-sm text-gray-400">Sales will appear here once the customer makes purchases</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Invoice</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Items</TableHead>
                                        <TableHead>Payment</TableHead>
                                        <TableHead className="text-right">Amount</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {customerSales.map((sale) => (
                                        <TableRow key={sale.id}>
                                            <TableCell>
                                                <Link
                                                    to={`/sales/details/${sale.id}`}
                                                    className="font-medium text-blue-600 hover:underline"
                                                >
                                                    {sale.invoiceNumber}
                                                </Link>
                                            </TableCell>
                                            <TableCell>
                                                {new Date(sale.date).toLocaleDateString('en-IN')}
                                            </TableCell>
                                            <TableCell>
                                                {sale.summary.itemCount} items
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline">
                                                    {sale.paymentMethod}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-right font-medium">
                                                ₹{sale.summary.grandTotal.toFixed(2)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default CustomerDetails;
