import { URLTester } from "@/components/URLTester"

export default function Settings() {
    return (
        <div className="flex flex-1 flex-col gap-6">
            <div>
                <h1 className="text-2xl font-bold mb-2">Settings</h1>
                <p className="text-muted-foreground">
                    Configure your Kirana Shop application settings and test URL handling.
                </p>
            </div>

            <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
                {/* URL Testing Section */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold">URL Handling Test</h2>
                    <URLTester />
                </div>

                {/* Other Settings */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold">Application Settings</h2>
                    <div className="space-y-4">
                        <div className="p-4 border rounded-lg">
                            <h3 className="font-medium mb-2">General Settings</h3>
                            <p className="text-sm text-muted-foreground">
                                General application configuration options will be available here.
                            </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                            <h3 className="font-medium mb-2">User Preferences</h3>
                            <p className="text-sm text-muted-foreground">
                                User-specific settings and preferences will be available here.
                            </p>
                        </div>
                        
                        <div className="p-4 border rounded-lg">
                            <h3 className="font-medium mb-2">System Configuration</h3>
                            <p className="text-sm text-muted-foreground">
                                System-level configuration options will be available here.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
