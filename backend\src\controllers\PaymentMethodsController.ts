import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { PaymentMethods } from "../models/PaymentMethods.model";

export const getPaymentMethods = async (req: Request, res: Response) => {
    try {
        const paymentMethodsRepository = AppDataSource.getRepository(PaymentMethods);
        const paymentMethods = await paymentMethodsRepository.find();
        if (!paymentMethods) {
            res.status(404).json({ type: "error", message: "Payment Methods Not Found" })
            return;
        }
        res.json({
            type: "success",
            data: paymentMethods
        });
        return;
    } catch (e) {
        console.log(e);
        res.status(500).json({ type: "error", message: "Internal Server Error" })
        return;
    }
}