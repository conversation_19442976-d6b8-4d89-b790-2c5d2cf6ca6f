import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    Index,
} from "typeorm";
import { Sales } from "./Sales.model";

@Entity("gst_breakdown")
export class GstBreakdown {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Sales, (sale) => sale.gstBreakdown, { onDelete: "CASCADE" })
    @JoinColumn({ name: "sale_id" })
    @Index("idx_gst_breakdown_sale")
    sale!: Sales;

    @Column("decimal", { precision: 5, scale: 2, name: "gst_rate" })
    @Index("idx_gst_rate")
    gstRate!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "taxable_amount" })
    taxableAmount!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "gst_amount" })
    gstAmount!: number;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    // Virtual properties for calculations
    get effectiveRate(): number {
        return this.taxableAmount > 0 ? (this.gstAmount / this.taxableAmount) * 100 : 0;
    }

    get totalAmount(): number {
        return this.taxableAmount + this.gstAmount;
    }

    get gstPercentageOfTotal(): number {
        const total = this.totalAmount;
        return total > 0 ? (this.gstAmount / total) * 100 : 0;
    }
}
