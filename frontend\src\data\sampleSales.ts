// Sample sales data for Kirana shop
export interface SaleItem {
    id: string
    name: string
    quantity: number
    price: number
    total: number
    gstRate: number
    gstAmount: number
    unit: string
    category: string
}

export interface Sale {
    id: string
    invoiceNumber: string
    date: string
    time: string
    customer: {
        id: string
        name: string
        phone: string
        category: 'Regular' | 'Wholesale' | 'VIP'
    } | null
    items: SaleItem[]
    paymentMethod: 'Cash' | 'UPI' | 'Credit'
    summary: {
        subtotal: number
        totalGst: number
        grandTotal: number
        itemCount: number
        gstBreakdown: {
            rate: number
            taxableAmount: number
            gstAmount: number
        }[]
    }
    status: 'Completed' | 'Pending' | 'Cancelled'
    createdBy: string
}

export const sampleSales: Sale[] = [
    {
        id: "1",
        invoiceNumber: "KGS2506251001",
        date: "2024-06-25",
        time: "09:15:30",
        customer: {
            id: "1",
            name: "<PERSON><PERSON>",
            phone: "9876543210",
            category: "Regular"
        },
        items: [
            {
                id: "1",
                name: "Tata Salt",
                quantity: 2,
                price: 22.00,
                total: 44.00,
                gstRate: 5,
                gstAmount: 2.20,
                unit: "kg",
                category: "Grocery"
            },
            {
                id: "2",
                name: "Maggi Noodles",
                quantity: 5,
                price: 14.00,
                total: 70.00,
                gstRate: 12,
                gstAmount: 8.40,
                unit: "pack",
                category: "Instant Food"
            }
        ],
        paymentMethod: "Cash",
        summary: {
            subtotal: 114.00,
            totalGst: 10.60,
            grandTotal: 124.60,
            itemCount: 7,
            gstBreakdown: [
                { rate: 5, taxableAmount: 44.00, gstAmount: 2.20 },
                { rate: 12, taxableAmount: 70.00, gstAmount: 8.40 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "2",
        invoiceNumber: "KGS2506251002",
        date: "2024-06-25",
        time: "10:30:45",
        customer: null,
        items: [
            {
                id: "3",
                name: "Coca Cola",
                quantity: 3,
                price: 40.00,
                total: 120.00,
                gstRate: 28,
                gstAmount: 33.60,
                unit: "bottle",
                category: "Beverages"
            }
        ],
        paymentMethod: "UPI",
        summary: {
            subtotal: 120.00,
            totalGst: 33.60,
            grandTotal: 153.60,
            itemCount: 3,
            gstBreakdown: [
                { rate: 28, taxableAmount: 120.00, gstAmount: 33.60 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "3",
        invoiceNumber: "KGS2506251003",
        date: "2024-06-25",
        time: "11:45:20",
        customer: {
            id: "2",
            name: "Priya Sharma",
            phone: "9876543211",
            category: "VIP"
        },
        items: [
            {
                id: "4",
                name: "Basmati Rice",
                quantity: 5,
                price: 120.00,
                total: 600.00,
                gstRate: 5,
                gstAmount: 30.00,
                unit: "kg",
                category: "Grocery"
            },
            {
                id: "5",
                name: "Sunflower Oil",
                quantity: 2,
                price: 180.00,
                total: 360.00,
                gstRate: 5,
                gstAmount: 18.00,
                unit: "liter",
                category: "Cooking Oil"
            }
        ],
        paymentMethod: "Credit",
        summary: {
            subtotal: 960.00,
            totalGst: 48.00,
            grandTotal: 1008.00,
            itemCount: 7,
            gstBreakdown: [
                { rate: 5, taxableAmount: 960.00, gstAmount: 48.00 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "4",
        invoiceNumber: "KGS2506251004",
        date: "2024-06-25",
        time: "14:20:15",
        customer: {
            id: "3",
            name: "Suresh Patel",
            phone: "9876543212",
            category: "Wholesale"
        },
        items: [
            {
                id: "6",
                name: "Britannia Biscuits",
                quantity: 10,
                price: 25.00,
                total: 250.00,
                gstRate: 18,
                gstAmount: 45.00,
                unit: "pack",
                category: "Snacks"
            },
            {
                id: "7",
                name: "Amul Milk",
                quantity: 4,
                price: 28.00,
                total: 112.00,
                gstRate: 5,
                gstAmount: 5.60,
                unit: "liter",
                category: "Dairy"
            }
        ],
        paymentMethod: "Cash",
        summary: {
            subtotal: 362.00,
            totalGst: 50.60,
            grandTotal: 412.60,
            itemCount: 14,
            gstBreakdown: [
                { rate: 5, taxableAmount: 112.00, gstAmount: 5.60 },
                { rate: 18, taxableAmount: 250.00, gstAmount: 45.00 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "5",
        invoiceNumber: "KGS2506241005",
        date: "2024-06-24",
        time: "16:30:00",
        customer: {
            id: "4",
            name: "Anjali Singh",
            phone: "9876543213",
            category: "Regular"
        },
        items: [
            {
                id: "8",
                name: "Surf Excel",
                quantity: 1,
                price: 85.00,
                total: 85.00,
                gstRate: 18,
                gstAmount: 15.30,
                unit: "pack",
                category: "Detergent"
            }
        ],
        paymentMethod: "UPI",
        summary: {
            subtotal: 85.00,
            totalGst: 15.30,
            grandTotal: 100.30,
            itemCount: 1,
            gstBreakdown: [
                { rate: 18, taxableAmount: 85.00, gstAmount: 15.30 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "6",
        invoiceNumber: "KGS2506241006",
        date: "2024-06-24",
        time: "18:45:30",
        customer: null,
        items: [
            {
                id: "9",
                name: "Parle-G Biscuits",
                quantity: 8,
                price: 12.00,
                total: 96.00,
                gstRate: 18,
                gstAmount: 17.28,
                unit: "pack",
                category: "Snacks"
            },
            {
                id: "10",
                name: "Pepsi",
                quantity: 2,
                price: 40.00,
                total: 80.00,
                gstRate: 28,
                gstAmount: 22.40,
                unit: "bottle",
                category: "Beverages"
            }
        ],
        paymentMethod: "Cash",
        summary: {
            subtotal: 176.00,
            totalGst: 39.68,
            grandTotal: 215.68,
            itemCount: 10,
            gstBreakdown: [
                { rate: 18, taxableAmount: 96.00, gstAmount: 17.28 },
                { rate: 28, taxableAmount: 80.00, gstAmount: 22.40 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    },
    {
        id: "7",
        invoiceNumber: "KGS2506231007",
        date: "2024-06-23",
        time: "12:15:45",
        customer: {
            id: "5",
            name: "Vikram Gupta",
            phone: "9876543214",
            category: "VIP"
        },
        items: [
            {
                id: "11",
                name: "Ghee",
                quantity: 1,
                price: 450.00,
                total: 450.00,
                gstRate: 5,
                gstAmount: 22.50,
                unit: "kg",
                category: "Dairy"
            }
        ],
        paymentMethod: "Credit",
        summary: {
            subtotal: 450.00,
            totalGst: 22.50,
            grandTotal: 472.50,
            itemCount: 1,
            gstBreakdown: [
                { rate: 5, taxableAmount: 450.00, gstAmount: 22.50 }
            ]
        },
        status: "Completed",
        createdBy: "Admin"
    }
]

// Helper functions for sales analytics
export const getSalesAnalytics = (sales: Sale[]) => {
    const today = new Date().toISOString().split('T')[0]
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    const todaySales = sales.filter(sale => sale.date === today && sale.status === 'Completed')
    const yesterdaySales = sales.filter(sale => sale.date === yesterday && sale.status === 'Completed')
    
    const todayTotal = todaySales.reduce((sum, sale) => sum + sale.summary.grandTotal, 0)
    const yesterdayTotal = yesterdaySales.reduce((sum, sale) => sum + sale.summary.grandTotal, 0)
    
    const totalSales = sales.filter(sale => sale.status === 'Completed').reduce((sum, sale) => sum + sale.summary.grandTotal, 0)
    const totalTransactions = sales.filter(sale => sale.status === 'Completed').length
    
    const paymentMethodBreakdown = sales.reduce((acc, sale) => {
        if (sale.status === 'Completed') {
            acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.summary.grandTotal
        }
        return acc
    }, {} as Record<string, number>)
    
    return {
        todayTotal,
        yesterdayTotal,
        todaySales: todaySales.length,
        yesterdaySales: yesterdaySales.length,
        totalSales,
        totalTransactions,
        averageOrderValue: totalTransactions > 0 ? totalSales / totalTransactions : 0,
        paymentMethodBreakdown
    }
}
