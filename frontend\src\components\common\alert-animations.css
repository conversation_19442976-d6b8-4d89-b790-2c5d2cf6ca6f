/* Enhanced Alert Animations */

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutToTop {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for animations */
.animate-shimmer {
  animation: shimmer 3s infinite;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.4s ease-out;
}

.animate-slide-out-top {
  animation: slideOutToTop 0.3s ease-in;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

/* Enhanced hover effects */
.alert-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Icon animations */
.alert-icon-bounce {
  animation: bounce-in 0.6s ease-out;
}

.alert-icon-pulse {
  animation: pulse-glow 2s infinite;
}

/* Progress bar animations */
.progress-bar-smooth {
  transition: width 0.1s linear;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .alert-mobile-adjust {
    margin: 0 1rem;
    max-width: calc(100vw - 2rem);
  }
}
