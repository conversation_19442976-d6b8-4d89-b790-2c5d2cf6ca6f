import { useState, type ChangeEvent, type FormEvent } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { User, Phone, MapPin, Building } from "lucide-react";
import { SupplierApiService } from "@/services/SupplierApi";
import { useGlobalAlert } from "@/components/common/GlobalAlert";

interface SupplierFormData {
    name: string;
    phone: string;
    address: string;
    gstNumber: string;
    totalPurchases: number;
    totalAmount: number;
    status: number;
}

const initialFormState: SupplierFormData = {
    name: "",
    phone: "",
    address: "",
    gstNumber: "",
    totalPurchases: 0,
    totalAmount: 0,
    status: 1
};

export default function AddSupplier({ onClose, refetchSuppliers }: { onClose: () => void } & { refetchSuppliers: () => void }) {
    const alert = useGlobalAlert();
    const [newSupplier, setNewSupplier] = useState<SupplierFormData>(initialFormState);
    const [errors, setErrors] = useState<Partial<SupplierFormData>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { id, value } = e.target;
        setNewSupplier(prev => ({
            ...prev,
            [id]: value
        }));
        if (errors[id as keyof SupplierFormData]) {
            setErrors(prev => ({ ...prev, [id]: undefined }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: Partial<SupplierFormData> = {};

        if (!newSupplier.name.trim()) newErrors.name = "Name is required";
        if (!newSupplier.phone.trim()) newErrors.phone = "Phone Number is required";
        if (!newSupplier.address.trim()) newErrors.address = "Address is required";

        if (newSupplier.phone.trim() && !/^\d{10}$/.test(newSupplier.phone)) {
            newErrors.phone = "Please enter a valid 10-digit phone number";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleAddSupplier = async (e: FormEvent) => {
        e.preventDefault();
        if (!validateForm()) return;

        setIsSubmitting(true);
        try {
            const result = await SupplierApiService.addSupplier(newSupplier);
            if (!result.success) {
                alert.showError(result.message || "Failed to add supplier");
            } else {
                alert.showSuccess(result.message || "Supplier added successfully");
            }
            onClose();
        } catch (error) {
            console.error("Error adding supplier:", error);
        } finally {
            setIsSubmitting(false);
            refetchSuppliers();
        }
    };

    return (
        <div className="w-full max-w-2xl mx-auto">
            {/* <h2 className="text-2xl font-bold text-center mb-6">Add New Supplier</h2> */}
            <form onSubmit={handleAddSupplier} className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                    <div className="space-y-2">
                        <Label htmlFor="name" className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            Supplier Name <span className="text-red-600">*</span>
                        </Label>
                        <Input
                            id="name"
                            placeholder="Enter supplier name"
                            value={newSupplier.name}
                            onChange={handleInputChange}
                            className={`transition-all ${errors.name ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
                        />
                        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="phone" className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Phone Number <span className="text-red-600">*</span>
                        </Label>
                        <Input
                            id="phone"
                            placeholder="10-digit phone number"
                            value={newSupplier.phone}
                            onChange={handleInputChange}
                            className={`transition-all ${errors.phone ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
                        />
                        {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="gstNumber" className="flex items-center gap-2">
                            <Building className="h-4 w-4" />
                            GST Number
                        </Label>
                        <Input
                            id="gstNumber"
                            placeholder="Enter GST number"
                            value={newSupplier.gstNumber}
                            onChange={handleInputChange}
                            className="transition-all hover:border-primary/50"
                        />
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="address" className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        Address <span className="text-red-600">*</span>
                    </Label>
                    <Textarea
                        id="address"
                        placeholder="Enter complete address"
                        value={newSupplier.address}
                        onChange={handleInputChange}
                        className={`transition-all min-h-[100px] ${errors.address ? "border-red-500 shadow-red-100" : "hover:border-primary/50"}`}
                    />
                    {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
                </div>

                <div className="flex justify-end gap-3 pt-6">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onClose}
                        className="w-24"
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        className="w-24"
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? "Adding..." : "Add"}
                    </Button>
                </div>
            </form>
        </div>
    );
}
