import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    Upload,
    Download,
    FileText,
    CheckCircle,
    AlertCircle,
    X,
    FileSpreadsheet,
    Database,
    Package,
    TrendingUp,
    BarChart3
} from "lucide-react"

// Mock data for export options
const exportOptions = [
    {
        id: "products",
        title: "Products Export",
        description: "Export all product information including SKU, name, category, price, and stock",
        icon: Package,
        recordCount: 1234,
        lastExported: "2024-01-19"
    },
    {
        id: "stock-levels",
        title: "Stock Levels Export",
        description: "Export current stock levels and low stock alerts",
        icon: TrendingUp,
        recordCount: 1234,
        lastExported: "2024-01-20"
    },
    {
        id: "stock-history",
        title: "Stock Movement History",
        description: "Export complete stock movement logs and history",
        icon: BarChart3,
        recordCount: 5678,
        lastExported: "2024-01-18"
    },
    {
        id: "categories-units",
        title: "Categories & Units",
        description: "Export product categories and units of measurement",
        icon: Database,
        recordCount: 45,
        lastExported: "2024-01-15"
    }
]

// Mock data for import history
const importHistory = [
    {
        id: 1,
        fileName: "products_bulk_import.csv",
        type: "Products",
        date: "2024-01-20 14:30",
        status: "Success",
        recordsProcessed: 150,
        recordsSuccess: 148,
        recordsError: 2,
        user: "Admin"
    },
    {
        id: 2,
        fileName: "stock_adjustment.csv",
        type: "Stock Adjustment",
        date: "2024-01-19 10:15",
        status: "Success",
        recordsProcessed: 75,
        recordsSuccess: 75,
        recordsError: 0,
        user: "Manager"
    },
    {
        id: 3,
        fileName: "new_categories.csv",
        type: "Categories",
        date: "2024-01-18 16:45",
        status: "Partial",
        recordsProcessed: 20,
        recordsSuccess: 18,
        recordsError: 2,
        user: "Admin"
    }
]

export default function ImportExport() {
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [importType, setImportType] = useState<string>("products")
    const [isImporting, setIsImporting] = useState(false)
    const [isExporting, setIsExporting] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)
    const [successMessage, setSuccessMessage] = useState("")
    const [showError, setShowError] = useState(false)
    const [errorMessage, setErrorMessage] = useState("")

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            if (file.type === "text/csv" || file.name.endsWith('.csv')) {
                setSelectedFile(file)
                setShowError(false)
            } else {
                setShowError(true)
                setErrorMessage("Please select a CSV file")
                setSelectedFile(null)
            }
        }
    }

    const handleImport = async () => {
        if (!selectedFile) {
            setShowError(true)
            setErrorMessage("Please select a file to import")
            return
        }

        setIsImporting(true)
        setShowError(false)

        try {
            // Simulate file processing
            await new Promise(resolve => setTimeout(resolve, 3000))
            
            setSuccessMessage(`Successfully imported ${selectedFile.name}. 95 records processed, 93 successful, 2 errors.`)
            setShowSuccess(true)
            setSelectedFile(null)
            
            // Reset file input
            const fileInput = document.getElementById('file-input') as HTMLInputElement
            if (fileInput) fileInput.value = ''
            
            setTimeout(() => setShowSuccess(false), 5000)
        } catch (error) {
            setShowError(true)
            setErrorMessage("Failed to import file. Please try again.")
        } finally {
            setIsImporting(false)
        }
    }

    const handleExport = async (exportType: string) => {
        setIsExporting(true)
        
        try {
            // Simulate export generation
            await new Promise(resolve => setTimeout(resolve, 2000))
            
            const option = exportOptions.find(opt => opt.id === exportType)
            setSuccessMessage(`${option?.title} exported successfully! Download will start automatically.`)
            setShowSuccess(true)
            
            // Simulate file download
            console.log(`Exporting ${exportType}...`)
            
            setTimeout(() => setShowSuccess(false), 3000)
        } catch (error) {
            setShowError(true)
            setErrorMessage("Failed to export data. Please try again.")
        } finally {
            setIsExporting(false)
        }
    }

    const downloadTemplate = (type: string) => {
        console.log(`Downloading ${type} template...`)
        // TODO: Implement template download
    }

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "Success":
                return (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Success
                    </Badge>
                )
            case "Partial":
                return (
                    <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Partial
                    </Badge>
                )
            case "Error":
                return (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                        <X className="h-3 w-3 mr-1" />
                        Error
                    </Badge>
                )
            default:
                return <Badge variant="secondary">{status}</Badge>
        }
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold">Import & Export</h1>
                <p className="text-muted-foreground">
                    Bulk import products and export inventory data for backup or analysis.
                </p>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{successMessage}</AlertDescription>
                </Alert>
            )}

            {/* Error Alert */}
            {showError && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errorMessage}</AlertDescription>
                </Alert>
            )}

            <div className="grid gap-6 lg:grid-cols-2">
                {/* Import Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Upload className="h-5 w-5" />
                            Import Data
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="import-type">Import Type</Label>
                                <select
                                    id="import-type"
                                    value={importType}
                                    onChange={(e) => setImportType(e.target.value)}
                                    className="w-full p-2 border rounded-md"
                                >
                                    <option value="products">Products</option>
                                    <option value="stock-adjustment">Stock Adjustment</option>
                                    <option value="categories">Categories</option>
                                    <option value="units">Units</option>
                                </select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="file-input">Select CSV File</Label>
                                <Input
                                    id="file-input"
                                    type="file"
                                    accept=".csv"
                                    onChange={handleFileSelect}
                                    className="cursor-pointer"
                                />
                                {selectedFile && (
                                    <div className="flex items-center gap-2 text-sm text-green-600">
                                        <FileSpreadsheet className="h-4 w-4" />
                                        <span>{selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)</span>
                                    </div>
                                )}
                            </div>

                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => downloadTemplate(importType)}
                                    className="flex-1"
                                >
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Template
                                </Button>
                                <Button
                                    onClick={handleImport}
                                    disabled={!selectedFile || isImporting}
                                    className="flex-1"
                                >
                                    {isImporting ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Importing...
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="h-4 w-4 mr-2" />
                                            Import Data
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>

                        <div className="border-t pt-4">
                            <h4 className="font-medium mb-2">Import Guidelines:</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                                <li>• Use CSV format only</li>
                                <li>• Download template for correct format</li>
                                <li>• Maximum file size: 10MB</li>
                                <li>• Duplicate SKUs will be updated</li>
                                <li>• Invalid data will be skipped</li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>

                {/* Export Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Download className="h-5 w-5" />
                            Export Data
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {exportOptions.map((option) => {
                                const IconComponent = option.icon
                                return (
                                    <div key={option.id} className="p-4 border rounded-lg">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start gap-3">
                                                <div className="p-2 bg-blue-50 rounded-lg">
                                                    <IconComponent className="h-5 w-5 text-blue-600" />
                                                </div>
                                                <div>
                                                    <h4 className="font-medium">{option.title}</h4>
                                                    <p className="text-sm text-muted-foreground mb-2">
                                                        {option.description}
                                                    </p>
                                                    <div className="flex gap-4 text-xs text-muted-foreground">
                                                        <span>{option.recordCount.toLocaleString()} records</span>
                                                        <span>Last exported: {option.lastExported}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleExport(option.id)}
                                                disabled={isExporting}
                                            >
                                                {isExporting ? (
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                                ) : (
                                                    <Download className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Import History */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Import History
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>File Name</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Date</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Records</TableHead>
                                <TableHead>Success Rate</TableHead>
                                <TableHead>User</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {importHistory.map((record) => (
                                <TableRow key={record.id}>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <FileSpreadsheet className="h-4 w-4 text-green-600" />
                                            <span className="font-medium">{record.fileName}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell>{record.type}</TableCell>
                                    <TableCell className="text-muted-foreground">{record.date}</TableCell>
                                    <TableCell>{getStatusBadge(record.status)}</TableCell>
                                    <TableCell>{record.recordsProcessed}</TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm">
                                                {Math.round((record.recordsSuccess / record.recordsProcessed) * 100)}%
                                            </span>
                                            <span className="text-xs text-muted-foreground">
                                                ({record.recordsSuccess}/{record.recordsProcessed})
                                            </span>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-muted-foreground">{record.user}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    )
}
