import { useState, useCallback } from "react"

interface UseLoaderReturn {
  isLoading: boolean
  startLoading: () => void
  stopLoading: () => void
  setLoading: (loading: boolean) => void
  withLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>
}

/**
 * Custom hook for managing loading states
 * 
 * @param initialState - Initial loading state (default: false)
 * @returns Object with loading state and control functions
 * 
 * @example
 * const { isLoading, startLoading, stopLoading, withLoading } = useLoader()
 * 
 * // Manual control
 * startLoading()
 * // ... do something
 * stopLoading()
 * 
 * // Automatic control with async function
 * await withLoading(async () => {
 *   const data = await fetchData()
 *   return data
 * })
 */
export const useLoader = (initialState: boolean = false): UseLoaderReturn => {
  const [isLoading, setIsLoading] = useState(initialState)

  const startLoading = useCallback(() => {
    setIsLoading(true)
  }, [])

  const stopLoading = useCallback(() => {
    setIsLoading(false)
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading)
  }, [])

  const withLoading = useCallback(async <T>(asyncFn: () => Promise<T>): Promise<T> => {
    try {
      setIsLoading(true)
      const result = await asyncFn()
      return result
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    isLoading,
    startLoading,
    stopLoading,
    setLoading,
    withLoading
  }
}

export default useLoader
