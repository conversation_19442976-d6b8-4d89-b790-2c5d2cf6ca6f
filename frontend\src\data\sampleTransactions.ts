// Sample transactions for dashboard
export interface Sale {
    id: string
    billNumber: string
    customerId?: string
    customerName?: string
    items: {
        productId: string
        productName: string
        quantity: number
        price: number
        total: number
    }[]
    subtotal: number
    gstAmount: number
    total: number
    paymentMethod: 'Cash' | 'UPI' | 'Credit'
    timestamp: string
    status: 'Completed' | 'Pending' | 'Cancelled'
}

export interface Purchase {
    id: string
    invoiceNumber: string
    supplierId: string
    supplierName: string
    items: {
        productId: string
        productName: string
        quantity: number
        price: number
        total: number
    }[]
    subtotal: number
    gstAmount: number
    total: number
    timestamp: string
    status: 'Completed' | 'Pending'
}

export interface UdhaarTransaction {
    id: string
    customerId: string
    customerName: string
    type: 'Given' | 'Collected'
    amount: number
    description: string
    timestamp: string
    dueDate?: string
}

// Sample sales for today
export const todaysSales: Sale[] = [
    {
        id: "S001",
        billNumber: "BILL-001",
        customerId: "1",
        customerName: "<PERSON><PERSON>",
        items: [
            { productId: "1", productName: "Basmati Rice", quantity: 2, price: 95, total: 190 },
            { productId: "5", productName: "Toor Dal", quantity: 1, price: 140, total: 140 },
            { productId: "18", productName: "Sugar", quantity: 2, price: 50, total: 100 }
        ],
        subtotal: 430,
        gstAmount: 21.50,
        total: 451.50,
        paymentMethod: "Cash",
        timestamp: "2024-06-25T09:15:00",
        status: "Completed"
    },
    {
        id: "S002",
        billNumber: "BILL-002",
        items: [
            { productId: "25", productName: "Biscuits (Parle-G)", quantity: 5, price: 20, total: 100 },
            { productId: "21", productName: "Tea Leaves", quantity: 0.25, price: 480, total: 120 }
        ],
        subtotal: 220,
        gstAmount: 15.40,
        total: 235.40,
        paymentMethod: "UPI",
        timestamp: "2024-06-25T10:30:00",
        status: "Completed"
    },
    {
        id: "S003",
        billNumber: "BILL-003",
        customerId: "2",
        customerName: "Priya Sharma",
        items: [
            { productId: "10", productName: "Sunflower Oil", quantity: 3, price: 165, total: 495 },
            { productId: "2", productName: "Wheat Flour", quantity: 10, price: 42, total: 420 },
            { productId: "13", productName: "Turmeric Powder", quantity: 0.5, price: 145, total: 72.50 }
        ],
        subtotal: 987.50,
        gstAmount: 49.38,
        total: 1036.88,
        paymentMethod: "Credit",
        timestamp: "2024-06-25T11:45:00",
        status: "Completed"
    }
]

// Sample recent purchases
export const recentPurchases: Purchase[] = [
    {
        id: "P001",
        invoiceNumber: "INV-2024-001",
        supplierId: "SUP001",
        supplierName: "Rajasthan Grains Supplier",
        items: [
            { productId: "1", productName: "Basmati Rice", quantity: 50, price: 80, total: 4000 },
            { productId: "2", productName: "Wheat Flour", quantity: 100, price: 35, total: 3500 }
        ],
        subtotal: 7500,
        gstAmount: 375,
        total: 7875,
        timestamp: "2024-06-24T14:30:00",
        status: "Completed"
    },
    {
        id: "P002",
        invoiceNumber: "INV-2024-002",
        supplierId: "SUP002",
        supplierName: "Spice World Distributors",
        items: [
            { productId: "13", productName: "Turmeric Powder", quantity: 5, price: 120, total: 600 },
            { productId: "14", productName: "Red Chili Powder", quantity: 3, price: 180, total: 540 }
        ],
        subtotal: 1140,
        gstAmount: 57,
        total: 1197,
        timestamp: "2024-06-23T16:15:00",
        status: "Completed"
    }
]

// Sample udhaar transactions
export const recentUdhaarTransactions: UdhaarTransaction[] = [
    {
        id: "U001",
        customerId: "1",
        customerName: "Ramesh Kumar",
        type: "Given",
        amount: 1200,
        description: "Monthly grocery credit",
        timestamp: "2024-06-25T11:45:00",
        dueDate: "2024-07-10"
    },
    {
        id: "U002",
        customerId: "3",
        customerName: "Suresh Patel",
        type: "Collected",
        amount: 800,
        description: "Partial payment received",
        timestamp: "2024-06-24T15:20:00"
    },
    {
        id: "U003",
        customerId: "5",
        customerName: "Rajesh Gupta",
        type: "Given",
        amount: 650,
        description: "Emergency purchase credit",
        timestamp: "2024-06-23T18:30:00",
        dueDate: "2024-07-08"
    }
]

// Helper functions for dashboard metrics
export const getTodaysSalesTotal = () => {
    return todaysSales.reduce((total, sale) => total + sale.total, 0)
}

export const getTodaysSalesCount = () => {
    return todaysSales.length
}

export const getTotalPendingUdhaar = () => {
    const givenAmount = recentUdhaarTransactions
        .filter(t => t.type === 'Given')
        .reduce((total, t) => total + t.amount, 0)
    
    const collectedAmount = recentUdhaarTransactions
        .filter(t => t.type === 'Collected')
        .reduce((total, t) => total + t.amount, 0)
    
    return givenAmount - collectedAmount
}
