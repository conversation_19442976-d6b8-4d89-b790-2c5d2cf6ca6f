import { DataSource, Repository } from "typeorm";
import { Category } from "../models/Category.model";
import { Units } from "../models/Units.model";

export type CategoryType = {
    id?: number;
    name: string;
    description?: string;
    productCount?: number;
    status: number;
}

export type UnitType = {
    id?: number;
    name: string;
    description?: string;
    productCount?: number;
    status: number;
}

export class CategoryService {
    private categoryRepository: Repository<Category>;

    constructor(dataSource: DataSource) {
        this.categoryRepository = dataSource.getRepository(Category);
    }

    async getAllCategories(): Promise<CategoryType[]> {
        return await this.categoryRepository.find();
    }

    async getCategoryById(id: number): Promise<CategoryType | null> {
        return await this.categoryRepository.findOne({ where: { id } });
    }

    async addCategory(category: CategoryType): Promise<CategoryType> {
        const newCategory = this.categoryRepository.create(category);
        return await this.categoryRepository.save(newCategory);
    }

    async updateCategory(id: number, category: CategoryType): Promise<CategoryType> {
        const existingCategory = await this.categoryRepository.findOne({ where: { id } });
        if (!existingCategory) {
            throw new Error("Category not found");
        }
        Object.assign(existingCategory, category);
        return await this.categoryRepository.save(existingCategory);
    }

    async deleteCategory(id: number): Promise<void> {
        await this.categoryRepository.delete(id);
    }

    async toggleCategoryStatus(id: number): Promise<CategoryType> {
        const existingCategory = await this.categoryRepository.findOne({ where: { id } });
        if (!existingCategory) {
            throw new Error("Category not found");
        }
        existingCategory.status = existingCategory.status === 1 ? 0 : 1;
        return await this.categoryRepository.save(existingCategory);
    }

}

export class UnitService {
    private unitRepository: Repository<Units>;

    constructor(dataSource: DataSource) {
        this.unitRepository = dataSource.getRepository(Units);
    }

    async getAllUnits(): Promise<UnitType[]> {
        return await this.unitRepository.find();
    }

    async getUnitById(id: number): Promise<UnitType | null> {
        return await this.unitRepository.findOne({ where: { id } });
    }

    async addUnit(unit: UnitType): Promise<UnitType> {
        const newUnit = this.unitRepository.create(unit);
        return await this.unitRepository.save(newUnit);
    }

    async updateUnit(id: number, unit: UnitType): Promise<UnitType> {
        const existingUnit = await this.unitRepository.findOne({ where: { id } });
        if (!existingUnit) {
            throw new Error("Unit not found");
        }
        Object.assign(existingUnit, unit);
        return await this.unitRepository.save(existingUnit);
    }

    async deleteUnit(id: number): Promise<void> {
        await this.unitRepository.delete(id);
    }

    async toggleUnitStatus(id: number): Promise<UnitType> {
        const existingUnit = await this.unitRepository.findOne({ where: { id } });
        if (!existingUnit) {
            throw new Error("Unit not found");
        }
        existingUnit.status = existingUnit.status === 1 ? 0 : 1;
        return await this.unitRepository.save(existingUnit);
    }

    // check if unit exists or not 
    private async unitExists(unit: Partial<Units>, fetchUnit: boolean = false): Promise<boolean | Units | null> {
        const existingUnit = await this.unitRepository.findOne({ where: unit });
        return fetchUnit ? existingUnit : !!existingUnit;
    }


}