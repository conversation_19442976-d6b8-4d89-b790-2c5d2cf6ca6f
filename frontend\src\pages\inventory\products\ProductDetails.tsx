import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    ArrowLeft,
    Edit,
    Trash2,
    Package,
    DollarSign,
    AlertTriangle,
    Calendar,
    Tag,
    BarChart3,
    TrendingUp,
    TrendingDown,
    Activity
} from "lucide-react"

// Mock data for the product
const mockProduct = {
    id: 1,
    name: "Basmati Rice 1kg",
    sku: "BR001",
    category: "Grains",
    unit: "kg",
    stock: 45,
    price: 120,
    lowStockThreshold: 10,
    description: "Premium quality Basmati rice, perfect for biryanis and pulao. Sourced from the finest farms in Punjab.",
    status: "Active",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    totalValue: 5400, // stock * price
    averageMonthlySales: 25,
    lastSaleDate: "2024-01-19"
}

// Mock stock history data
const stockHistory = [
    {
        id: 1,
        date: "2024-01-20",
        type: "Stock In",
        quantity: 20,
        previousStock: 25,
        newStock: 45,
        reason: "Purchase from supplier",
        user: "Admin"
    },
    {
        id: 2,
        date: "2024-01-19",
        type: "Sale",
        quantity: -5,
        previousStock: 30,
        newStock: 25,
        reason: "Customer purchase",
        user: "Cashier"
    },
    {
        id: 3,
        date: "2024-01-18",
        type: "Adjustment",
        quantity: -2,
        previousStock: 32,
        newStock: 30,
        reason: "Damaged goods",
        user: "Manager"
    },
    {
        id: 4,
        date: "2024-01-15",
        type: "Stock In",
        quantity: 32,
        previousStock: 0,
        newStock: 32,
        reason: "Initial stock",
        user: "Admin"
    }
]

export default function ProductDetails() {
    const navigate = useNavigate()
    const { id } = useParams()
    const [product, setProduct] = useState(mockProduct)
    const [isLoading, setIsLoading] = useState(false)

    useEffect(() => {
        // In a real app, you would fetch the product by ID from an API
        setIsLoading(true)
        setTimeout(() => {
            setIsLoading(false)
        }, 500)
    }, [id])

    const getStockStatus = () => {
        if (product.stock === 0) {
            return { label: "Out of Stock", color: "bg-red-100 text-red-800", icon: AlertTriangle }
        } else if (product.stock <= product.lowStockThreshold) {
            return { label: "Low Stock", color: "bg-orange-100 text-orange-800", icon: AlertTriangle }
        } else {
            return { label: "In Stock", color: "bg-green-100 text-green-800", icon: Package }
        }
    }

    const getStockChangeIcon = (type: string) => {
        switch (type) {
            case "Stock In":
                return <TrendingUp className="h-4 w-4 text-green-600" />
            case "Sale":
                return <TrendingDown className="h-4 w-4 text-blue-600" />
            case "Adjustment":
                return <Activity className="h-4 w-4 text-orange-600" />
            default:
                return <Activity className="h-4 w-4 text-gray-600" />
        }
    }

    const stockStatus = getStockStatus()
    const StatusIcon = stockStatus.icon

    if (isLoading) {
        return (
            <div className="flex flex-1 items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate("/inventory/products")}
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Products
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">{product.name}</h1>
                        <p className="text-muted-foreground">SKU: {product.sku}</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/inventory/products/edit/${product.id}`)}
                    >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                    </Button>
                </div>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
                {/* Product Overview */}
                <div className="md:col-span-2 space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Product Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Category</p>
                                    <p className="text-sm">{product.category}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Unit</p>
                                    <p className="text-sm">{product.unit}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                                    <Badge className={stockStatus.color}>
                                        <StatusIcon className="h-3 w-3 mr-1" />
                                        {stockStatus.label}
                                    </Badge>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Low Stock Alert</p>
                                    <p className="text-sm">{product.lowStockThreshold} {product.unit}</p>
                                </div>
                            </div>
                            {product.description && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground mb-2">Description</p>
                                    <p className="text-sm">{product.description}</p>
                                </div>
                            )}
                            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Created</p>
                                    <p className="text-sm">{new Date(product.createdAt).toLocaleDateString()}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                                    <p className="text-sm">{new Date(product.updatedAt).toLocaleDateString()}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stock History */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Stock History</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Change</TableHead>
                                        <TableHead>Stock</TableHead>
                                        <TableHead>Reason</TableHead>
                                        <TableHead>User</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {stockHistory.map((entry) => (
                                        <TableRow key={entry.id}>
                                            <TableCell className="text-sm">
                                                {new Date(entry.date).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    {getStockChangeIcon(entry.type)}
                                                    <span className="text-sm">{entry.type}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className={`text-sm font-medium ${
                                                    entry.quantity > 0 ? 'text-green-600' : 'text-red-600'
                                                }`}>
                                                    {entry.quantity > 0 ? '+' : ''}{entry.quantity}
                                                </span>
                                            </TableCell>
                                            <TableCell className="text-sm">
                                                {entry.previousStock} → {entry.newStock}
                                            </TableCell>
                                            <TableCell className="text-sm text-muted-foreground">
                                                {entry.reason}
                                            </TableCell>
                                            <TableCell className="text-sm text-muted-foreground">
                                                {entry.user}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>

                {/* Stats Sidebar */}
                <div className="space-y-6">
                    {/* Current Stats */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Current Stats</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Package className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm font-medium">Current Stock</span>
                                </div>
                                <span className="text-lg font-bold">{product.stock}</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <DollarSign className="h-4 w-4 text-green-600" />
                                    <span className="text-sm font-medium">Unit Price</span>
                                </div>
                                <span className="text-lg font-bold">₹{product.price}</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <BarChart3 className="h-4 w-4 text-purple-600" />
                                    <span className="text-sm font-medium">Total Value</span>
                                </div>
                                <span className="text-lg font-bold">₹{product.totalValue.toLocaleString()}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Performance Stats */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Performance</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <div className="flex items-center gap-2 mb-1">
                                    <TrendingUp className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm font-medium">Avg Monthly Sales</span>
                                </div>
                                <span className="text-lg font-bold">{product.averageMonthlySales} units</span>
                            </div>
                            <div>
                                <div className="flex items-center gap-2 mb-1">
                                    <Calendar className="h-4 w-4 text-orange-600" />
                                    <span className="text-sm font-medium">Last Sale</span>
                                </div>
                                <span className="text-sm">{new Date(product.lastSaleDate).toLocaleDateString()}</span>
                            </div>
                            <div>
                                <div className="flex items-center gap-2 mb-1">
                                    <AlertTriangle className="h-4 w-4 text-red-600" />
                                    <span className="text-sm font-medium">Days Until Low Stock</span>
                                </div>
                                <span className="text-lg font-bold">
                                    {Math.floor((product.stock - product.lowStockThreshold) / (product.averageMonthlySales / 30))} days
                                </span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Button variant="outline" size="sm" className="w-full justify-start">
                                <TrendingUp className="h-4 w-4 mr-2" />
                                Adjust Stock
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start">
                                <Tag className="h-4 w-4 mr-2" />
                                Update Price
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start">
                                <BarChart3 className="h-4 w-4 mr-2" />
                                View Reports
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
