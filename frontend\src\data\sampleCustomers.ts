// Sample customers for Kirana shop
export interface Customer {
    id: string
    name: string
    phone: string
    address: string
    category: 'Regular' | 'Wholesale' | 'VIP'
    creditLimit: number
    currentCredit: number
    totalPurchases: number
    lastPurchase: string
    joinDate: string
}

export const sampleCustomers: Customer[] = [
    {
        id: "1",
        name: "<PERSON><PERSON>",
        phone: "9876543210",
        address: "Main Market, Sadulpur",
        category: "Regular",
        creditLimit: 5000,
        currentCredit: 2500,
        totalPurchases: 45000,
        lastPurchase: "2024-06-24",
        joinDate: "2023-01-15"
    },
    {
        id: "2",
        name: "<PERSON><PERSON>",
        phone: "9876543211",
        address: "Gandhi Chowk, Sadulpur",
        category: "Wholesale",
        creditLimit: 15000,
        currentCredit: 1800,
        totalPurchases: 125000,
        lastPurchase: "2024-06-25",
        joinDate: "2022-08-20"
    },
    {
        id: "3",
        name: "<PERSON><PERSON>",
        phone: "9876543212",
        address: "Station Road, Sadulpur",
        category: "Regular",
        creditLimit: 3000,
        currentCredit: 950,
        totalPurchases: 28000,
        lastPurchase: "2024-06-23",
        joinDate: "2023-05-10"
    },
    {
        id: "4",
        name: "<PERSON> Devi",
        phone: "9876543213",
        address: "Civil Lines, Sadulpur",
        category: "VIP",
        creditLimit: 10000,
        currentCredit: 0,
        totalPurchases: 85000,
        lastPurchase: "2024-06-25",
        joinDate: "2022-03-12"
    },
    {
        id: "5",
        name: "Rajesh Gupta",
        phone: "9876543214",
        address: "Bus Stand Area, Sadulpur",
        category: "Regular",
        creditLimit: 4000,
        currentCredit: 1200,
        totalPurchases: 32000,
        lastPurchase: "2024-06-24",
        joinDate: "2023-02-28"
    },
    {
        id: "6",
        name: "Meera Jain",
        phone: "9876543215",
        address: "Old City, Sadulpur",
        category: "Wholesale",
        creditLimit: 20000,
        currentCredit: 5500,
        totalPurchases: 180000,
        lastPurchase: "2024-06-25",
        joinDate: "2021-11-05"
    },
    {
        id: "7",
        name: "Vikram Singh",
        phone: "9876543216",
        address: "New Colony, Sadulpur",
        category: "Regular",
        creditLimit: 2500,
        currentCredit: 0,
        totalPurchases: 18000,
        lastPurchase: "2024-06-22",
        joinDate: "2023-09-15"
    },
    {
        id: "8",
        name: "Sunita Agarwal",
        phone: "9876543217",
        address: "Market Road, Sadulpur",
        category: "Regular",
        creditLimit: 3500,
        currentCredit: 800,
        totalPurchases: 25000,
        lastPurchase: "2024-06-24",
        joinDate: "2023-04-18"
    },
    {
        id: "9",
        name: "Deepak Sharma",
        phone: "9876543218",
        address: "Hospital Road, Sadulpur",
        category: "VIP",
        creditLimit: 8000,
        currentCredit: 2200,
        totalPurchases: 65000,
        lastPurchase: "2024-06-25",
        joinDate: "2022-07-30"
    },
    {
        id: "10",
        name: "Kavita Kumari",
        phone: "9876543219",
        address: "School Street, Sadulpur",
        category: "Regular",
        creditLimit: 2000,
        currentCredit: 450,
        totalPurchases: 15000,
        lastPurchase: "2024-06-23",
        joinDate: "2023-12-08"
    }
]

// Helper function to get customers with overdue payments
export const getOverdueCustomers = () => {
    return sampleCustomers.filter(customer => customer.currentCredit > 0)
}

// Helper function to get customer categories summary
export const getCustomerCategorySummary = () => {
    const summary = sampleCustomers.reduce((acc, customer) => {
        acc[customer.category] = (acc[customer.category] || 0) + 1
        return acc
    }, {} as Record<string, number>)
    
    return summary
}
