import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
    JoinColumn,
    Index,
} from "typeorm";
import { Customer } from "./Customer.model";
import { SaleItem } from "./SaleItem.model";
import { GstBreakdown } from "./GstBreakdown.model";
import { PaymentTransaction } from "./PaymentTransaction.model";
import { CreditTransaction } from "./CreditTransaction.model";

@Entity("sales")
export class Sales {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "varchar", length: 50, unique: true, name: "invoice_number" })
    @Index("idx_invoice_number")
    invoiceNumber!: string;

    @Column({ type: "varchar", length: 50, name: "bill_number" })
    billNumber!: string;

    @ManyToOne(() => Customer, { nullable: true })
    @JoinColumn({ name: "customer_id" })
    @Index("idx_customer_sale")
    customer?: Customer;

    @Column({ type: "varchar", length: 255, nullable: true, name: "customer_name" })
    customerName?: string;

    @Column({ type: "varchar", length: 15, nullable: true, name: "customer_phone" })
    customerPhone?: string;

    @Column({ type: "date", name: "sale_date" })
    @Index("idx_sale_date")
    saleDate!: Date;

    @Column({ type: "time", name: "sale_time" })
    saleTime!: string;

    @Column({
        type: "enum",
        enum: ["Cash", "UPI", "Credit", "Card"],
        name: "payment_method"
    })
    @Index("idx_payment_method")
    paymentMethod!: "Cash" | "UPI" | "Credit" | "Card";

    @Column({
        type: "enum",
        enum: ["Paid", "Pending", "Partial"],
        default: "Paid",
        name: "payment_status"
    })
    paymentStatus!: "Paid" | "Pending" | "Partial";

    @Column("decimal", { precision: 10, scale: 2 })
    subtotal!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "total_gst" })
    totalGst!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "discount_amount" })
    discountAmount!: number;

    @Column("decimal", { precision: 5, scale: 2, default: 0.00, name: "discount_percentage" })
    discountPercentage!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "grand_total" })
    grandTotal!: number;

    @Column({ type: "int", name: "total_items" })
    totalItems!: number;

    @Column({
        type: "enum",
        enum: ["Completed", "Pending", "Cancelled", "Draft"],
        default: "Completed"
    })
    @Index("idx_sale_status")
    status!: "Completed" | "Pending" | "Cancelled" | "Draft";

    @Column({ type: "text", nullable: true })
    notes?: string;

    @Column({ type: "varchar", length: 100, default: "Admin", name: "created_by" })
    createdBy!: string;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    @UpdateDateColumn({
        name: "updated_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    updatedAt!: Date;

    // Relations
    @OneToMany(() => SaleItem, (saleItem) => saleItem.sale, { cascade: true })
    items!: SaleItem[];

    @OneToMany(() => GstBreakdown, (gstBreakdown) => gstBreakdown.sale, { cascade: true })
    gstBreakdown!: GstBreakdown[];

    @OneToMany(() => PaymentTransaction, (paymentTransaction) => paymentTransaction.sale, { cascade: true })
    paymentTransactions!: PaymentTransaction[];

    @OneToMany(() => CreditTransaction, (creditTransaction) => creditTransaction.sale)
    creditTransactions!: CreditTransaction[];

    // Virtual properties for API responses
    get isWalkInCustomer(): boolean {
        return !this.customer && !!this.customerName;
    }

    get averageItemValue(): number {
        return this.totalItems > 0 ? this.grandTotal / this.totalItems : 0;
    }

    get gstPercentage(): number {
        return this.subtotal > 0 ? (this.totalGst / this.subtotal) * 100 : 0;
    }

    get profitMargin(): number {
        // This would need to be calculated based on purchase prices
        // For now, return 0 as placeholder
        return 0;
    }
}
