import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, User, Phone, Mail, MapPin, CreditCard, Building, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { GlobalAlert } from '@/components/common/GlobalAlert';
import { customerApi, CreateCustomerRequest } from '@/services/customerApi';

interface FormData extends CreateCustomerRequest {
    confirmPhone: string;
}

interface FormErrors {
    [key: string]: string;
}

const AddCustomer: React.FC = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [alertState, setAlertState] = useState<{
        show: boolean;
        message: string;
        type: 'success' | 'error' | 'warning';
    }>({ show: false, message: '', type: 'success' });

    const [formData, setFormData] = useState<FormData>({
        name: '',
        phone: '',
        confirmPhone: '',
        email: '',
        address: '',
        category: 'Regular',
        creditLimit: 0,
        gstNumber: '',
        businessName: '',
        notes: ''
    });

    const [errors, setErrors] = useState<FormErrors>({});

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};

        // Name validation
        if (!formData.name.trim()) {
            newErrors.name = 'Customer name is required';
        } else if (formData.name.trim().length < 2) {
            newErrors.name = 'Name must be at least 2 characters long';
        }

        // Phone validation
        if (!formData.phone.trim()) {
            newErrors.phone = 'Phone number is required';
        } else if (!/^[6-9]\d{9}$/.test(formData.phone)) {
            newErrors.phone = 'Please enter a valid 10-digit Indian mobile number';
        }

        // Confirm phone validation
        if (formData.phone !== formData.confirmPhone) {
            newErrors.confirmPhone = 'Phone numbers do not match';
        }

        // Email validation (optional)
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        // Credit limit validation
        if (formData.creditLimit && formData.creditLimit < 0) {
            newErrors.creditLimit = 'Credit limit cannot be negative';
        }

        // GST number validation (optional)
        if (formData.gstNumber && !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(formData.gstNumber)) {
            newErrors.gstNumber = 'Please enter a valid GST number';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field: keyof FormData, value: string | number) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        
        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    const showAlert = (message: string, type: 'success' | 'error' | 'warning') => {
        setAlertState({ show: true, message, type });
        setTimeout(() => setAlertState(prev => ({ ...prev, show: false })), 3000);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            showAlert('Please fix the errors in the form', 'error');
            return;
        }

        setLoading(true);
        try {
            // Remove confirmPhone from the data sent to API
            const { confirmPhone, ...customerData } = formData;
            
            // For now, simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // In real implementation:
            // await customerApi.createCustomer(customerData);
            
            showAlert('Customer added successfully!', 'success');
            
            // Navigate back to customer list after a short delay
            setTimeout(() => {
                navigate('/customers/list');
            }, 1500);
            
        } catch (error) {
            console.error('Error adding customer:', error);
            showAlert('Failed to add customer. Please try again.', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleReset = () => {
        setFormData({
            name: '',
            phone: '',
            confirmPhone: '',
            email: '',
            address: '',
            category: 'Regular',
            creditLimit: 0,
            gstNumber: '',
            businessName: '',
            notes: ''
        });
        setErrors({});
    };

    return (
        <div className="space-y-6">
            {/* Alert */}
            {alertState.show && (
                <GlobalAlert 
                    message={alertState.message} 
                    type={alertState.type} 
                />
            )}

            {/* Header */}
            <div className="flex items-center gap-4">
                <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => navigate('/customers/list')}
                    className="flex items-center gap-2"
                >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Customers
                </Button>
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Add New Customer</h1>
                    <p className="text-gray-600 mt-1">Register a new customer in your system</p>
                </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <User className="h-5 w-5" />
                            Basic Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Customer Name *</Label>
                                <Input
                                    id="name"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    placeholder="Enter customer name"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="category">Customer Category</Label>
                                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Regular">Regular</SelectItem>
                                        <SelectItem value="Wholesale">Wholesale</SelectItem>
                                        <SelectItem value="VIP">VIP</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number *</Label>
                                <div className="relative">
                                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input
                                        id="phone"
                                        value={formData.phone}
                                        onChange={(e) => handleInputChange('phone', e.target.value.replace(/\D/g, '').slice(0, 10))}
                                        placeholder="Enter 10-digit mobile number"
                                        className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
                                        maxLength={10}
                                    />
                                </div>
                                {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="confirmPhone">Confirm Phone Number *</Label>
                                <div className="relative">
                                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input
                                        id="confirmPhone"
                                        value={formData.confirmPhone}
                                        onChange={(e) => handleInputChange('confirmPhone', e.target.value.replace(/\D/g, '').slice(0, 10))}
                                        placeholder="Re-enter phone number"
                                        className={`pl-10 ${errors.confirmPhone ? 'border-red-500' : ''}`}
                                        maxLength={10}
                                    />
                                </div>
                                {errors.confirmPhone && <p className="text-sm text-red-500">{errors.confirmPhone}</p>}
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="email">Email Address (Optional)</Label>
                            <div className="relative">
                                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                    id="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => handleInputChange('email', e.target.value)}
                                    placeholder="Enter email address"
                                    className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                                />
                            </div>
                            {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="address">Address</Label>
                            <div className="relative">
                                <MapPin className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
                                <Textarea
                                    id="address"
                                    value={formData.address}
                                    onChange={(e) => handleInputChange('address', e.target.value)}
                                    placeholder="Enter customer address"
                                    className="pl-10 min-h-[80px]"
                                    rows={3}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Business Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Building className="h-5 w-5" />
                            Business Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="businessName">Business Name (Optional)</Label>
                                <Input
                                    id="businessName"
                                    value={formData.businessName}
                                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                                    placeholder="Enter business name"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="gstNumber">GST Number (Optional)</Label>
                                <Input
                                    id="gstNumber"
                                    value={formData.gstNumber}
                                    onChange={(e) => handleInputChange('gstNumber', e.target.value.toUpperCase())}
                                    placeholder="Enter GST number"
                                    className={errors.gstNumber ? 'border-red-500' : ''}
                                    maxLength={15}
                                />
                                {errors.gstNumber && <p className="text-sm text-red-500">{errors.gstNumber}</p>}
                                <p className="text-xs text-gray-500">Format: 22AAAAA0000A1Z5</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Credit Information */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5" />
                            Credit Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="creditLimit">Credit Limit (₹)</Label>
                            <Input
                                id="creditLimit"
                                type="number"
                                value={formData.creditLimit}
                                onChange={(e) => handleInputChange('creditLimit', parseFloat(e.target.value) || 0)}
                                placeholder="Enter credit limit"
                                className={errors.creditLimit ? 'border-red-500' : ''}
                                min="0"
                                step="100"
                            />
                            {errors.creditLimit && <p className="text-sm text-red-500">{errors.creditLimit}</p>}
                            <p className="text-xs text-gray-500">
                                Set to 0 for cash-only customers. Recommended limits: Regular (₹5,000), Wholesale (₹15,000), VIP (₹25,000)
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Additional Notes */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            Additional Notes
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            <Label htmlFor="notes">Notes (Optional)</Label>
                            <Textarea
                                id="notes"
                                value={formData.notes}
                                onChange={(e) => handleInputChange('notes', e.target.value)}
                                placeholder="Add any additional notes about the customer..."
                                className="min-h-[100px]"
                                rows={4}
                            />
                            <p className="text-xs text-gray-500">
                                You can add special instructions, preferences, or any other relevant information
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Form Actions */}
                <div className="flex items-center justify-between pt-6 border-t">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleReset}
                        disabled={loading}
                    >
                        Reset Form
                    </Button>

                    <div className="flex items-center gap-3">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => navigate('/customers/list')}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading}
                            className="flex items-center gap-2"
                        >
                            {loading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    Adding Customer...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4" />
                                    Add Customer
                                </>
                            )}
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
};

export default AddCustomer;
