import { useState, useEffect, useRef } from "react"
import { BrowserMultiFormatReader, NotFoundException } from "@zxing/library"
import { 
    Camera, 
    CameraOff, 
    Scan, 
    X, 
    AlertCircle, 
    CheckCircle,
    Smartphone,
    Monitor
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface BarcodeScannerProps {
    onBarcodeScanned: (barcode: string) => void
    isOpen: boolean
    onClose: () => void
}

interface ScanHistory {
    barcode: string
    timestamp: Date
    productFound: boolean
}

export default function BarcodeScanner({ onBarcodeScanned, isOpen, onClose }: BarcodeScannerProps) {
    const [isScanning, setIsScanning] = useState(false)
    const [manualBarcode, setManualBarcode] = useState("")
    const [error, setError] = useState<string | null>(null)
    const [scanHistory, setScanHistory] = useState<ScanHistory[]>([])
    const [hasPermission, setHasPermission] = useState<boolean | null>(null)
    const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
    const [selectedDevice, setSelectedDevice] = useState<string>("")
    
    const videoRef = useRef<HTMLVideoElement>(null)
    const codeReader = useRef<BrowserMultiFormatReader | null>(null)

    // Initialize barcode reader
    useEffect(() => {
        if (isOpen) {
            codeReader.current = new BrowserMultiFormatReader()
            getVideoDevices()
        }
        
        return () => {
            stopScanning()
        }
    }, [isOpen])

    // Get available video devices
    const getVideoDevices = async () => {
        try {
            const videoDevices = await BrowserMultiFormatReader.listVideoInputDevices()
            setDevices(videoDevices)
            if (videoDevices.length > 0) {
                setSelectedDevice(videoDevices[0].deviceId)
            }
        } catch (err) {
            console.error("Error getting video devices:", err)
            setError("Unable to access camera devices")
        }
    }

    // Start camera scanning
    const startScanning = async () => {
        if (!codeReader.current || !videoRef.current) return

        try {
            setError(null)
            setIsScanning(true)

            // Request camera permission
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    deviceId: selectedDevice ? { exact: selectedDevice } : undefined,
                    facingMode: 'environment' // Use back camera on mobile
                } 
            })
            
            setHasPermission(true)
            
            // Start decoding from video stream
            await codeReader.current.decodeFromVideoDevice(
                selectedDevice || undefined,
                videoRef.current,
                (result, error) => {
                    if (result) {
                        const barcode = result.getText()
                        handleBarcodeFound(barcode)
                    }
                    if (error && !(error instanceof NotFoundException)) {
                        console.error("Scanning error:", error)
                    }
                }
            )
        } catch (err: any) {
            console.error("Camera error:", err)
            setHasPermission(false)
            setIsScanning(false)
            
            if (err.name === 'NotAllowedError') {
                setError("Camera permission denied. Please allow camera access and try again.")
            } else if (err.name === 'NotFoundError') {
                setError("No camera found. Please use manual barcode entry.")
            } else {
                setError("Unable to start camera. Please try manual entry.")
            }
        }
    }

    // Stop camera scanning
    const stopScanning = () => {
        if (codeReader.current) {
            codeReader.current.reset()
        }
        setIsScanning(false)
    }

    // Handle barcode detection
    const handleBarcodeFound = (barcode: string) => {
        // Add to scan history
        const newScan: ScanHistory = {
            barcode,
            timestamp: new Date(),
            productFound: true // This would be determined by actual product lookup
        }
        setScanHistory(prev => [newScan, ...prev.slice(0, 4)]) // Keep last 5 scans

        // Notify parent component
        onBarcodeScanned(barcode)
        
        // Stop scanning after successful scan
        stopScanning()
        
        // Auto-close after short delay
        setTimeout(() => {
            onClose()
        }, 1000)
    }

    // Handle manual barcode entry
    const handleManualEntry = () => {
        if (manualBarcode.trim()) {
            handleBarcodeFound(manualBarcode.trim())
            setManualBarcode("")
        }
    }

    // Handle device change
    const handleDeviceChange = (deviceId: string) => {
        setSelectedDevice(deviceId)
        if (isScanning) {
            stopScanning()
            setTimeout(() => startScanning(), 100)
        }
    }

    if (!isOpen) return null

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <Scan className="h-5 w-5" />
                                Barcode Scanner
                            </CardTitle>
                            <CardDescription>
                                Scan product barcodes using your camera or enter manually
                            </CardDescription>
                        </div>
                        <Button variant="outline" size="sm" onClick={onClose}>
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                    {/* Camera Scanner */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="font-medium">Camera Scanner</h3>
                            {devices.length > 1 && (
                                <select 
                                    value={selectedDevice}
                                    onChange={(e) => handleDeviceChange(e.target.value)}
                                    className="text-sm border rounded px-2 py-1"
                                >
                                    {devices.map((device) => (
                                        <option key={device.deviceId} value={device.deviceId}>
                                            {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                                        </option>
                                    ))}
                                </select>
                            )}
                        </div>

                        {/* Video Preview */}
                        <div className="relative bg-black rounded-lg overflow-hidden">
                            <video
                                ref={videoRef}
                                className="w-full h-64 object-cover"
                                style={{ display: isScanning ? 'block' : 'none' }}
                            />
                            
                            {!isScanning && (
                                <div className="w-full h-64 flex items-center justify-center bg-gray-100">
                                    <div className="text-center">
                                        <Camera className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                                        <p className="text-gray-600">Camera preview will appear here</p>
                                    </div>
                                </div>
                            )}

                            {/* Scanning overlay */}
                            {isScanning && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="w-48 h-32 border-2 border-green-500 rounded-lg relative">
                                        <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-green-500"></div>
                                        <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-green-500"></div>
                                        <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-green-500"></div>
                                        <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-green-500"></div>
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <p className="text-white text-sm bg-black/50 px-2 py-1 rounded">
                                                Position barcode here
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Camera Controls */}
                        <div className="flex gap-2">
                            {!isScanning ? (
                                <Button onClick={startScanning} className="flex-1">
                                    <Camera className="h-4 w-4 mr-2" />
                                    Start Camera
                                </Button>
                            ) : (
                                <Button onClick={stopScanning} variant="destructive" className="flex-1">
                                    <CameraOff className="h-4 w-4 mr-2" />
                                    Stop Camera
                                </Button>
                            )}
                        </div>

                        {/* Error Display */}
                        {error && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        {/* Permission Status */}
                        {hasPermission === false && (
                            <Alert>
                                <Smartphone className="h-4 w-4" />
                                <AlertDescription>
                                    Camera access is required for barcode scanning. Please enable camera permissions in your browser settings.
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    {/* Manual Entry */}
                    <div className="space-y-4 border-t pt-4">
                        <h3 className="font-medium">Manual Entry</h3>
                        <div className="flex gap-2">
                            <Input
                                placeholder="Enter barcode manually..."
                                value={manualBarcode}
                                onChange={(e) => setManualBarcode(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleManualEntry()}
                                className="flex-1"
                            />
                            <Button onClick={handleManualEntry} disabled={!manualBarcode.trim()}>
                                <Scan className="h-4 w-4 mr-2" />
                                Add
                            </Button>
                        </div>
                    </div>

                    {/* Scan History */}
                    {scanHistory.length > 0 && (
                        <div className="space-y-4 border-t pt-4">
                            <h3 className="font-medium">Recent Scans</h3>
                            <div className="space-y-2">
                                {scanHistory.map((scan, index) => (
                                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <div>
                                            <p className="font-mono text-sm">{scan.barcode}</p>
                                            <p className="text-xs text-gray-500">
                                                {scan.timestamp.toLocaleTimeString()}
                                            </p>
                                        </div>
                                        <Badge variant={scan.productFound ? "default" : "secondary"}>
                                            {scan.productFound ? (
                                                <CheckCircle className="h-3 w-3 mr-1" />
                                            ) : (
                                                <AlertCircle className="h-3 w-3 mr-1" />
                                            )}
                                            {scan.productFound ? "Found" : "Not Found"}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
