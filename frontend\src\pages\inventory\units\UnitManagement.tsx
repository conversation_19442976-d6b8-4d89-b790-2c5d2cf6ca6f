import { useState } from "react"
import { use<PERSON>avigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    Plus,
    Search,
    Edit,
    Trash2,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    CheckCircle,
    AlertCircle,
    ArrowLeft,
    Tag
} from "lucide-react"

// Mock data for units
const mockUnits = [
    { id: 1, name: "kg", description: "Kilogram", productCount: 25, status: "Active", createdAt: "2024-01-15" },
    { id: 2, name: "g", description: "Gram", productCount: 8, status: "Active", createdAt: "2024-01-15" },
    { id: 3, name: "l", description: "Liter", productCount: 12, status: "Active", createdAt: "2024-01-15" },
    { id: 4, name: "ml", description: "Milliliter", productCount: 15, status: "Active", createdAt: "2024-01-16" },
    { id: 5, name: "pack", description: "Package/Pack", productCount: 18, status: "Active", createdAt: "2024-01-16" },
    { id: 6, name: "piece", description: "Individual piece", productCount: 6, status: "Active", createdAt: "2024-01-17" },
    { id: 7, name: "dozen", description: "12 pieces", productCount: 3, status: "Active", createdAt: "2024-01-17" },
    { id: 8, name: "box", description: "Box/Carton", productCount: 0, status: "Inactive", createdAt: "2024-01-18" }
]

export default function UnitManagement() {
    const navigate = useNavigate()
    const [units, setUnits] = useState(mockUnits)
    const [searchTerm, setSearchTerm] = useState("")
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [selectedUnit, setSelectedUnit] = useState<any>(null)
    const [showSuccess, setShowSuccess] = useState(false)
    const [successMessage, setSuccessMessage] = useState("")

    const [formData, setFormData] = useState({
        name: "",
        description: "",
        status: "Active"
    })

    const resetForm = () => {
        setFormData({
            name: "",
            description: "",
            status: "Active"
        })
    }

    const handleAdd = () => {
        const newUnit = {
            id: Date.now(),
            ...formData,
            productCount: 0,
            createdAt: new Date().toISOString().split('T')[0]
        }

        setUnits([...units, newUnit])
        setIsAddDialogOpen(false)
        resetForm()
        setSuccessMessage("Unit added successfully!")
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const handleEdit = () => {
        setUnits(units.map(unit => 
            unit.id === selectedUnit.id ? { ...unit, ...formData } : unit
        ))
        setIsEditDialogOpen(false)
        setSelectedUnit(null)
        resetForm()
        setSuccessMessage("Unit updated successfully!")
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const handleDelete = () => {
        setUnits(units.filter(unit => unit.id !== selectedUnit.id))
        setIsDeleteDialogOpen(false)
        setSelectedUnit(null)
        setSuccessMessage("Unit deleted successfully!")
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 3000)
    }

    const openEditDialog = (unit: any) => {
        setSelectedUnit(unit)
        setFormData({
            name: unit.name,
            description: unit.description,
            status: unit.status
        })
        setIsEditDialogOpen(true)
    }

    const openDeleteDialog = (unit: any) => {
        setSelectedUnit(unit)
        setIsDeleteDialogOpen(true)
    }

    const filteredUnits = units.filter(unit =>
        unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unit.description.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const getStatusBadge = (status: string) => {
        return status === "Active" ? (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
            </Badge>
        ) : (
            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                Inactive
            </Badge>
        )
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate("/inventory/categories")}
                >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Categories
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Unit Management</h1>
                    <p className="text-muted-foreground">
                        Manage units of measurement for your products.
                    </p>
                </div>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{successMessage}</AlertDescription>
                </Alert>
            )}

            {/* Search and Add */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search units..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={resetForm}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Unit
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add New Unit</DialogTitle>
                            <DialogDescription>
                                Create a new unit of measurement for your products.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Unit Name *</Label>
                                <Input
                                    id="name"
                                    value={formData.name}
                                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                                    placeholder="e.g., kg, liter, pack"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                    placeholder="Enter unit description"
                                    rows={3}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleAdd} disabled={!formData.name.trim()}>
                                Add Unit
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Units Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Tag className="h-5 w-5" />
                            <span>Units ({filteredUnits.length})</span>
                        </div>
                        <div className="flex gap-2">
                            <Badge variant="secondary">
                                Active: {filteredUnits.filter(unit => unit.status === "Active").length}
                            </Badge>
                            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                                Inactive: {filteredUnits.filter(unit => unit.status === "Inactive").length}
                            </Badge>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredUnits.length === 0 ? (
                        <div className="text-center py-8">
                            <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No units found</h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm
                                    ? "Try adjusting your search criteria."
                                    : "Start by adding your first unit of measurement."
                                }
                            </p>
                            <Button onClick={() => setIsAddDialogOpen(true)}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Unit
                            </Button>
                        </div>
                    ) : (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Unit Name</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Products Using</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredUnits.map((unit) => (
                                    <TableRow key={unit.id}>
                                        <TableCell>
                                            <div className="font-medium">{unit.name}</div>
                                        </TableCell>
                                        <TableCell className="text-muted-foreground">
                                            {unit.description || "No description"}
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">
                                                {unit.productCount} products
                                            </Badge>
                                        </TableCell>
                                        <TableCell>{getStatusBadge(unit.status)}</TableCell>
                                        <TableCell className="text-muted-foreground text-sm">
                                            {new Date(unit.createdAt).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="sm">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={() => openEditDialog(unit)}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        onClick={() => openDeleteDialog(unit)}
                                                        className="text-red-600 focus:text-red-600"
                                                        disabled={unit.productCount > 0}
                                                    >
                                                        <Trash2 className="h-4 w-4 mr-2" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    )}
                </CardContent>
            </Card>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Unit</DialogTitle>
                        <DialogDescription>
                            Update the unit information.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="edit-name">Unit Name *</Label>
                            <Input
                                id="edit-name"
                                value={formData.name}
                                onChange={(e) => setFormData({...formData, name: e.target.value})}
                                placeholder="Enter unit name"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-description">Description</Label>
                            <Textarea
                                id="edit-description"
                                value={formData.description}
                                onChange={(e) => setFormData({...formData, description: e.target.value})}
                                placeholder="Enter description"
                                rows={3}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleEdit} disabled={!formData.name.trim()}>
                            Update Unit
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete Unit</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete "{selectedUnit?.name}"? This action cannot be undone.
                            {selectedUnit?.productCount > 0 && (
                                <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded">
                                    <div className="flex items-center gap-2 text-orange-800">
                                        <AlertCircle className="h-4 w-4" />
                                        <span className="text-sm">
                                            This unit is used by {selectedUnit.productCount} products and cannot be deleted.
                                        </span>
                                    </div>
                                </div>
                            )}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button 
                            variant="destructive" 
                            onClick={handleDelete}
                            disabled={selectedUnit?.productCount > 0}
                        >
                            Delete
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
