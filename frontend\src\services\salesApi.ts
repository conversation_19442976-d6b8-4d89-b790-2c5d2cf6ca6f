import { apiClient } from '@/lib/api';
import type { ApiResponse } from '@/lib/api';

// Types matching backend interfaces
export interface CreateSaleRequest {
    customerId?: number;
    customerName?: string;
    customerPhone?: string;
    paymentMethod: "Cash" | "UPI" | "Credit" | "Card";
    items: Array<{
        productId: number;
        quantity: number;
        unitPrice: number;
        gstRate: number;
        discountAmount?: number;
    }>;
    discountAmount?: number;
    discountPercentage?: number;
    notes?: string;
    paymentDetails?: {
        transactionId?: string;
        upiId?: string;
        referenceNumber?: string;
    };
}

export interface Sale {
    id: number;
    invoiceNumber: string;
    customerId?: number;
    customerName?: string;
    customerPhone?: string;
    paymentMethod: string;
    subtotal: number;
    discountAmount: number;
    discountPercentage: number;
    gstAmount: number;
    grandTotal: number;
    notes?: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    items: SaleItem[];
    gstBreakdown: GstBreakdown[];
    paymentTransactions: PaymentTransaction[];
}

export interface SaleItem {
    id: number;
    productId: number;
    productName: string;
    quantity: number;
    unitPrice: number;
    gstRate: number;
    gstAmount: number;
    discountAmount: number;
    totalAmount: number;
}

export interface GstBreakdown {
    id: number;
    gstRate: number;
    taxableAmount: number;
    gstAmount: number;
}

export interface PaymentTransaction {
    id: number;
    amount: number;
    method: string;
    transactionId?: string;
    upiId?: string;
    referenceNumber?: string;
    status: string;
    createdAt: string;
}

export interface SalesSummary {
    totalSales: number;
    totalTransactions: number;
    averageOrderValue: number;
    paymentMethodBreakdown: Array<{
        method: string;
        amount: number;
        percentage: number;
    }>;
    gstBreakdown: Array<{
        rate: number;
        taxableAmount: number;
        gstAmount: number;
    }>;
}

export interface DailySummary {
    date: string;
    totalSales: number;
    totalTransactions: number;
    averageOrderValue: number;
    cashSales: number;
    upiSales: number;
    creditSales: number;
    cardSales: number;
    totalGstCollected: number;
}

// Sales API service class
export class SalesApiService {
    // Create a new sale
    static async createSale(saleData: CreateSaleRequest): Promise<ApiResponse<Sale>> {
        return apiClient.post<Sale>('/sales', saleData);
    }

    // Get sales history with optional filters
    static async getSalesHistory(params?: {
        page?: number;
        limit?: number;
        startDate?: string;
        endDate?: string;
        customerId?: number;
        paymentMethod?: string;
        status?: string;
        search?: string;
    }): Promise<ApiResponse<Sale[]>> {
        return apiClient.get<Sale[]>('/sales', params);
    }

    // Get a specific sale by ID
    static async getSaleById(saleId: number): Promise<ApiResponse<Sale>> {
        return apiClient.get<Sale>(`/sales/${saleId}`);
    }

    // Get today's sales summary
    static async getTodaysSummary(): Promise<ApiResponse<DailySummary>> {
        return apiClient.get<DailySummary>('/sales/today-summary');
    }

    // Get sales summary for a date range
    static async getSalesSummary(params?: {
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponse<SalesSummary>> {
        return apiClient.get<SalesSummary>('/sales/summary', params);
    }

    // Get daily sales summary for a specific date
    static async getDailySummary(date: string): Promise<ApiResponse<DailySummary>> {
        return apiClient.get<DailySummary>(`/sales/daily-summary/${date}`);
    }

    // Update sale status (if needed)
    static async updateSaleStatus(saleId: number, status: string): Promise<ApiResponse<Sale>> {
        return apiClient.patch<Sale>(`/sales/${saleId}/status`, { status });
    }

    // Get sales analytics for dashboard
    static async getSalesAnalytics(params?: {
        period?: 'today' | 'week' | 'month' | 'year';
        startDate?: string;
        endDate?: string;
    }): Promise<ApiResponse<{
        totalSales: number;
        totalTransactions: number;
        averageOrderValue: number;
        growthPercentage: number;
        paymentMethodBreakdown: Array<{
            method: string;
            amount: number;
            percentage: number;
        }>;
        topProducts: Array<{
            productId: number;
            productName: string;
            totalQuantity: number;
            totalRevenue: number;
        }>;
        hourlyBreakdown: Array<{
            hour: number;
            sales: number;
            transactions: number;
        }>;
    }>> {
        return apiClient.get('/sales/analytics', params);
    }

    // Export sales data
    static async exportSales(params?: {
        format?: 'csv' | 'excel' | 'pdf';
        startDate?: string;
        endDate?: string;
        customerId?: number;
        paymentMethod?: string;
    }): Promise<ApiResponse<{ downloadUrl: string }>> {
        return apiClient.get('/sales/export', params);
    }
}

// Export default instance for convenience
export default SalesApiService;
