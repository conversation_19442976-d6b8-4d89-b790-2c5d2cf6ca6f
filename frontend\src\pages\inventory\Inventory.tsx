import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
    Package,
    Plus,
    Search,
    Filter,
    TrendingUp,
    AlertTriangle,
    Eye,
    Edit,
    Trash2,
    MoreHorizontal,
    Download,
    Upload,
    BarChart3,
    DollarSign,
    Package2,
    ChevronDown,
    SortAsc,
    SortDesc,
    RefreshCw,
    CheckCircle,
    History
} from "lucide-react"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

// Mock data for demonstration
const inventoryStats = [
    {
        title: "Total Products",
        value: "1,234",
        change: "+12%",
        trend: "up",
        icon: Package2,
        color: "text-blue-600"
    },
    {
        title: "Total Value",
        value: "₹2,45,678",
        change: "+8%",
        trend: "up",
        icon: DollarSign,
        color: "text-green-600"
    },
    {
        title: "Low Stock Items",
        value: "23",
        change: "-5%",
        trend: "down",
        icon: AlertTriangle,
        color: "text-orange-600"
    },
    {
        title: "Out of Stock",
        value: "8",
        change: "+2",
        trend: "up",
        icon: Package,
        color: "text-red-600"
    }
]

const quickActions = [
    {
        title: "Add New Product",
        description: "Add a new item to inventory",
        icon: Plus,
        color: "bg-blue-500 hover:bg-blue-600",
        action: "add-product",
        route: "/inventory/products/add"
    },
    {
        title: "Manage Products",
        description: "View and edit all products",
        icon: Package,
        color: "bg-green-500 hover:bg-green-600",
        action: "manage-products",
        route: "/inventory/products"
    },
    {
        title: "Stock Management",
        description: "Adjust stock levels",
        icon: TrendingUp,
        color: "bg-purple-500 hover:bg-purple-600",
        action: "stock-management",
        route: "/inventory/stock"
    },
    {
        title: "Categories & Units",
        description: "Manage categories and units",
        icon: BarChart3,
        color: "bg-orange-500 hover:bg-orange-600",
        action: "categories-units",
        route: "/inventory/categories"
    },
    {
        title: "Stock Logs",
        description: "View stock history",
        icon: Eye,
        color: "bg-indigo-500 hover:bg-indigo-600",
        action: "stock-logs",
        route: "/inventory/stock/logs"
    },
    {
        title: "Import/Export",
        description: "Bulk operations",
        icon: Upload,
        color: "bg-teal-500 hover:bg-teal-600",
        action: "import-export",
        route: "/inventory/import-export"
    }
]

// product list table 
const recentProducts = [
    {
        id: 1,
        name: "Basmati Rice 1kg",
        category: "Grains",
        stock: 45,
        price: 120,
        status: "In Stock",
        lastUpdated: "2 hours ago"
    },
    {
        id: 2,
        name: "Tata Salt 1kg",
        category: "Spices",
        stock: 8,
        price: 25,
        status: "Low Stock",
        lastUpdated: "4 hours ago"
    },
    {
        id: 3,
        name: "Amul Milk 500ml",
        category: "Dairy",
        stock: 0,
        price: 28,
        status: "Out of Stock",
        lastUpdated: "1 day ago"
    },
    {
        id: 4,
        name: "Maggi Noodles",
        category: "Instant Food",
        stock: 67,
        price: 15,
        status: "In Stock",
        lastUpdated: "3 hours ago"
    },
    {
        id: 5,
        name: "Britannia Biscuits",
        category: "Snacks",
        stock: 12,
        price: 35,
        status: "Low Stock",
        lastUpdated: "5 hours ago"
    }
]

export default function Inventory() {
    const navigate = useNavigate()
    const [searchTerm, setSearchTerm] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [sortBy, setSortBy] = useState<string>("name")
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
    const [filterStatus, setFilterStatus] = useState<string>("all")

    // Filter and sort products
    const filteredProducts = recentProducts
        .filter(product => {
            const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.category.toLowerCase().includes(searchTerm.toLowerCase())
            const matchesStatus = filterStatus === "all" || product.status === filterStatus
            return matchesSearch && matchesStatus
        })
        .sort((a, b) => {
            let aValue = a[sortBy as keyof typeof a]
            let bValue = b[sortBy as keyof typeof b]

            if (typeof aValue === "string") aValue = aValue.toLowerCase()
            if (typeof bValue === "string") bValue = bValue.toLowerCase()

            if (sortOrder === "asc") {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
            }
        })

    // refresh btn handle function
    const handleRefresh = async () => {
        setIsLoading(true)
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        setIsLoading(false)
    }

    const handleQuickAction = (action: string, route?: string) => {
        if (route) {
            navigate(route)
            return
        }

        switch (action) {
            case "add-product":
                navigate("/inventory/products/add")
                break
            case "manage-products":
                navigate("/inventory/products")
                break
            case "stock-management":
                navigate("/inventory/stock")
                break
            case "categories-units":
                navigate("/inventory/categories")
                break
            case "stock-logs":
                navigate("/inventory/stock/logs")
                break
            case "import-export":
                navigate("/inventory/import-export")
                break
            case "export":
                console.log("Export action")
                // TODO: Implement export functionality
                break
            default:
                console.log(`Executing action: ${action}`)
        }
    }

    // product list table action btn 
    const handleProductAction = (action: string, productId: number) => {
        console.log(`${action} product with ID: ${productId}`)
        // Add your product action handlers here
    }

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "In Stock":
                return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">In Stock</Badge>
            case "Low Stock":
                return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Low Stock</Badge>
            case "Out of Stock":
                return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Out of Stock</Badge>
            default:
                return <Badge variant="secondary">{status}</Badge>
        }
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold">Inventory Management</h1>
                    <p className="text-muted-foreground">
                        Manage your product inventory, track stock levels, and monitor performance.
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={isLoading}
                    >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleQuickAction('export')}>
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                    <Button size="sm" onClick={() => handleQuickAction('add-product')}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Product
                    </Button>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {inventoryStats.map((stat, index) => {
                    const IconComponent = stat.icon
                    return (
                        <Card key={index}>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">
                                            {stat.title}
                                        </p>
                                        <p className="text-2xl font-bold">{stat.value}</p>
                                        <div className="flex items-center gap-1 mt-1">
                                            {stat.trend === "up" ? (
                                                <TrendingUp className="h-3 w-3 text-green-600" />
                                            ) : (
                                                <TrendingUp className="h-3 w-3 text-red-600 rotate-180" />
                                            )}
                                            <span className={`text-xs ${stat.trend === "up" ? "text-green-600" : "text-red-600"
                                                }`}>
                                                {stat.change}
                                            </span>
                                        </div>
                                    </div>
                                    <div className={`p-3 rounded-full bg-muted/50`}>
                                        <IconComponent className={`h-5 w-5 ${stat.color}`} />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )
                })}
            </div>

            {/* Quick Actions */}
            <div>
                <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {quickActions.map((action, index) => (
                        <Card
                            key={index}
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => handleQuickAction(action.action, action.route)}
                        >
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4">
                                    <div className={`p-3 rounded-lg text-white ${action.color}`}>
                                        <action.icon className="h-5 w-5" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-sm">{action.title}</h3>
                                        <p className="text-xs text-muted-foreground">{action.description}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search products..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <div className="flex gap-2">
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                                All Status
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => setFilterStatus("In Stock")}>
                                In Stock
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterStatus("Low Stock")}>
                                Low Stock
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setFilterStatus("Out of Stock")}>
                                Out of Stock
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                                {sortOrder === "asc" ? <SortAsc className="h-4 w-4 mr-2" /> : <SortDesc className="h-4 w-4 mr-2" />}
                                Sort
                                <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => { setSortBy("name"); setSortOrder("asc") }}>
                                Name (A-Z)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("name"); setSortOrder("desc") }}>
                                Name (Z-A)
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => { setSortBy("stock"); setSortOrder("desc") }}>
                                Stock (High-Low)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("stock"); setSortOrder("asc") }}>
                                Stock (Low-High)
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => { setSortBy("price"); setSortOrder("desc") }}>
                                Price (High-Low)
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => { setSortBy("price"); setSortOrder("asc") }}>
                                Price (Low-High)
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>

            {/* Products Table */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Products ({filteredProducts.length})</span>
                        <Button variant="ghost" size="sm">
                            View All
                        </Button>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {filteredProducts.length === 0 ? (
                        <div className="text-center py-8">
                            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No products found</h3>
                            <p className="text-muted-foreground mb-4">
                                {searchTerm || filterStatus !== "all"
                                    ? "Try adjusting your search or filter criteria."
                                    : "Start by adding your first product to the inventory."
                                }
                            </p>
                            <Button onClick={() => handleQuickAction('add-product')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Product
                            </Button>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Product</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Category</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Stock</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Price</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Status</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Last Updated</th>
                                        <th className="text-left py-3 px-2 font-medium text-muted-foreground">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredProducts.map((product) => (
                                        <tr key={product.id} className="border-b hover:bg-muted/50">
                                            <td className="py-3 px-2">
                                                <div className="font-medium">{product.name}</div>
                                            </td>
                                            <td className="py-3 px-2 text-muted-foreground">{product.category}</td>
                                            <td className="py-3 px-2">
                                                <span className={`font-medium ${product.stock === 0 ? 'text-red-600' :
                                                    product.stock < 15 ? 'text-orange-600' : 'text-green-600'
                                                    }`}>
                                                    {product.stock}
                                                </span>
                                            </td>
                                            <td className="py-3 px-2">₹{product.price}</td>
                                            <td className="py-3 px-2">{getStatusBadge(product.status)}</td>
                                            <td className="py-3 px-2 text-muted-foreground text-sm">{product.lastUpdated}</td>
                                            <td className="py-3 px-2">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => handleProductAction('view', product.id)}>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            View Details
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem onClick={() => handleProductAction('edit', product.id)}>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit Product
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem
                                                            onClick={() => handleProductAction('delete', product.id)}
                                                            className="text-red-600 focus:text-red-600"
                                                        >
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Recent Activity & Alerts */}
            <div className="grid gap-6 md:grid-cols-2">
                {/* Low Stock Alerts */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-orange-600" />
                            Low Stock Alerts
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {recentProducts.filter(p => p.stock <= 15 && p.stock > 0).map((product) => (
                                <div key={product.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                                    <div>
                                        <p className="font-medium text-sm">{product.name}</p>
                                        <p className="text-xs text-muted-foreground">
                                            Current: {product.stock} | Alert at: 15
                                        </p>
                                    </div>
                                    <Button size="sm" variant="outline" onClick={() => navigate(`/inventory/products/edit/${product.id}`)}>
                                        Restock
                                    </Button>
                                </div>
                            ))}
                            {recentProducts.filter(p => p.stock <= 15 && p.stock > 0).length === 0 && (
                                <div className="text-center py-4 text-muted-foreground">
                                    <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                                    <p className="text-sm">All products are well stocked!</p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Stats */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <BarChart3 className="h-5 w-5 text-blue-600" />
                            Quick Stats
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Categories</span>
                                <span className="font-medium">8 active</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Units</span>
                                <span className="font-medium">7 types</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Avg. Stock Value</span>
                                <span className="font-medium">₹1,98,765</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Last Stock Update</span>
                                <span className="font-medium">2 hours ago</span>
                            </div>
                            <div className="pt-2 border-t">
                                <Button size="sm" variant="outline" className="w-full" onClick={() => navigate("/inventory/stock/logs")}>
                                    <History className="h-4 w-4 mr-2" />
                                    View All Activity
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

