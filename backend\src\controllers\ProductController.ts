import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { Product } from "../models/Product.model";

export const getAllProducts = async (req: Request, res: Response) => {
    try {
        const productsRepository = AppDataSource.getRepository(Product);
        const products = await productsRepository.find({
            relations: {
                category: true,
                unit: true,

            },
            select: {
                category: {
                    name: true,
                    id: true
                },
                unit: {
                    name: true,
                    id: true
                }
            }
        });
        if (!products) {
            res.status(500).json({ type: "error", message: "Failed to fetch products" });
            return;
        }
        res.status(200).json({ type: "success", message: "Products fetched successfully", data: products });
    } catch (e) {
        console.log("get all products error: ", e);
        res.status(500).json({ type: "error", message: "Failed to fetch products" });
    }
}

export const getSingleProduct = async (req: Request, res: Response) => {
    res.json("Single product Data");
}

export const addProduct = async (req: Request, res: Response) => {
    res.json("Product Added");
}

export const editProduct = async (req: Request, res: Response) => {
    res.json("Product Edited");
}

export const deleteProduct = async (req: Request, res: Response) => {
    res.json("Product Deleted");
}
