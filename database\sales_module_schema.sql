-- =====================================================
-- KIRANA SHOP MANAGEMENT SYSTEM - SALES MODULE SCHEMA
-- =====================================================
-- This file contains all database tables and structures needed for the Sales Module
-- Created for: Sales & Billing functionality with GST calculations, customer management, and reporting
-- Compatible with: MySQL 8.0+

-- =====================================================
-- 1. CUSTOMERS TABLE
-- =====================================================
-- Stores customer information for sales and credit management
CREATE TABLE IF NOT EXISTS `customers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `phone` VARCHAR(15) UNIQUE NOT NULL,
    `email` VARCHAR(255) NULL,
    `address` TEXT NULL,
    `category` ENUM('Regular', 'Wholesale', 'VIP') DEFAULT 'Regular',
    `credit_limit` DECIMAL(10,2) DEFAULT 0.00,
    `current_credit` DECIMAL(10,2) DEFAULT 0.00,
    `total_purchases` DECIMAL(12,2) DEFAULT 0.00,
    `total_transactions` INT DEFAULT 0,
    `loyalty_points` INT DEFAULT 0,
    `gst_number` VARCHAR(15) NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `last_purchase_date` DATETIME NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_customer_phone` (`phone`),
    INDEX `idx_customer_name` (`name`),
    INDEX `idx_customer_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. SALES TABLE (Main Sales/Invoice Table)
-- =====================================================
-- Stores main sales transaction information
CREATE TABLE IF NOT EXISTS `sales` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `invoice_number` VARCHAR(50) UNIQUE NOT NULL,
    `bill_number` VARCHAR(50) NOT NULL,
    `customer_id` INT NULL,
    `customer_name` VARCHAR(255) NULL, -- For walk-in customers
    `customer_phone` VARCHAR(15) NULL, -- For walk-in customers
    `sale_date` DATE NOT NULL,
    `sale_time` TIME NOT NULL,
    `payment_method` ENUM('Cash', 'UPI', 'Credit', 'Card') NOT NULL,
    `payment_status` ENUM('Paid', 'Pending', 'Partial') DEFAULT 'Paid',
    `subtotal` DECIMAL(10,2) NOT NULL,
    `total_gst` DECIMAL(10,2) DEFAULT 0.00,
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00,
    `discount_percentage` DECIMAL(5,2) DEFAULT 0.00,
    `grand_total` DECIMAL(10,2) NOT NULL,
    `total_items` INT NOT NULL,
    `status` ENUM('Completed', 'Pending', 'Cancelled', 'Draft') DEFAULT 'Completed',
    `notes` TEXT NULL,
    `created_by` VARCHAR(100) DEFAULT 'Admin',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL,
    INDEX `idx_sale_date` (`sale_date`),
    INDEX `idx_invoice_number` (`invoice_number`),
    INDEX `idx_payment_method` (`payment_method`),
    INDEX `idx_sale_status` (`status`),
    INDEX `idx_customer_sale` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. SALE_ITEMS TABLE
-- =====================================================
-- Stores individual items in each sale
CREATE TABLE IF NOT EXISTS `sale_items` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `sale_id` INT NOT NULL,
    `product_id` INT NOT NULL,
    `product_name` VARCHAR(255) NOT NULL, -- Snapshot for historical data
    `product_barcode` VARCHAR(50) NULL,
    `category_name` VARCHAR(100) NULL, -- Snapshot for reporting
    `unit_name` VARCHAR(50) NULL, -- Snapshot (kg, piece, liter, etc.)
    `quantity` DECIMAL(8,3) NOT NULL,
    `unit_price` DECIMAL(10,2) NOT NULL,
    `total_price` DECIMAL(10,2) NOT NULL,
    `gst_rate` DECIMAL(5,2) DEFAULT 0.00,
    `gst_amount` DECIMAL(10,2) DEFAULT 0.00,
    `discount_amount` DECIMAL(10,2) DEFAULT 0.00,
    `final_amount` DECIMAL(10,2) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE RESTRICT,
    INDEX `idx_sale_items_sale` (`sale_id`),
    INDEX `idx_sale_items_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. GST_BREAKDOWN TABLE
-- =====================================================
-- Stores GST breakdown for each sale (for reporting and compliance)
CREATE TABLE IF NOT EXISTS `gst_breakdown` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `sale_id` INT NOT NULL,
    `gst_rate` DECIMAL(5,2) NOT NULL,
    `taxable_amount` DECIMAL(10,2) NOT NULL,
    `gst_amount` DECIMAL(10,2) NOT NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
    INDEX `idx_gst_breakdown_sale` (`sale_id`),
    INDEX `idx_gst_rate` (`gst_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. PAYMENT_TRANSACTIONS TABLE
-- =====================================================
-- Stores detailed payment information for each sale
CREATE TABLE IF NOT EXISTS `payment_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `sale_id` INT NOT NULL,
    `payment_method` ENUM('Cash', 'UPI', 'Credit', 'Card') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `transaction_id` VARCHAR(100) NULL, -- For UPI/Card transactions
    `upi_id` VARCHAR(100) NULL, -- For UPI payments
    `reference_number` VARCHAR(100) NULL,
    `payment_status` ENUM('Success', 'Failed', 'Pending') DEFAULT 'Success',
    `payment_date` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `notes` TEXT NULL,
    
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
    INDEX `idx_payment_sale` (`sale_id`),
    INDEX `idx_payment_method` (`payment_method`),
    INDEX `idx_payment_date` (`payment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. CREDIT_TRANSACTIONS TABLE
-- =====================================================
-- Stores credit/udhaar transactions for customers
CREATE TABLE IF NOT EXISTS `credit_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `customer_id` INT NOT NULL,
    `sale_id` INT NULL, -- NULL for direct credit adjustments
    `transaction_type` ENUM('Credit_Given', 'Payment_Received', 'Adjustment') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `balance_before` DECIMAL(10,2) NOT NULL,
    `balance_after` DECIMAL(10,2) NOT NULL,
    `due_date` DATE NULL,
    `payment_method` ENUM('Cash', 'UPI', 'Card', 'Adjustment') NULL,
    `reference_number` VARCHAR(100) NULL,
    `description` TEXT NULL,
    `status` ENUM('Active', 'Paid', 'Overdue', 'Written_Off') DEFAULT 'Active',
    `created_by` VARCHAR(100) DEFAULT 'Admin',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE SET NULL,
    INDEX `idx_credit_customer` (`customer_id`),
    INDEX `idx_credit_type` (`transaction_type`),
    INDEX `idx_credit_status` (`status`),
    INDEX `idx_due_date` (`due_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. DAILY_SALES_SUMMARY TABLE
-- =====================================================
-- Stores daily aggregated sales data for quick reporting
CREATE TABLE IF NOT EXISTS `daily_sales_summary` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `summary_date` DATE UNIQUE NOT NULL,
    `total_sales` DECIMAL(12,2) DEFAULT 0.00,
    `total_transactions` INT DEFAULT 0,
    `cash_sales` DECIMAL(10,2) DEFAULT 0.00,
    `upi_sales` DECIMAL(10,2) DEFAULT 0.00,
    `credit_sales` DECIMAL(10,2) DEFAULT 0.00,
    `card_sales` DECIMAL(10,2) DEFAULT 0.00,
    `total_gst_collected` DECIMAL(10,2) DEFAULT 0.00,
    `total_items_sold` INT DEFAULT 0,
    `unique_customers` INT DEFAULT 0,
    `new_customers` INT DEFAULT 0,
    `average_order_value` DECIMAL(10,2) DEFAULT 0.00,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_summary_date` (`summary_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. INVOICE_SEQUENCES TABLE
-- =====================================================
-- Manages invoice number sequences and formats
CREATE TABLE IF NOT EXISTS `invoice_sequences` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `sequence_type` ENUM('Invoice', 'Bill', 'Credit_Note') NOT NULL,
    `prefix` VARCHAR(10) NOT NULL,
    `current_number` INT DEFAULT 1,
    `date_format` VARCHAR(20) DEFAULT 'YYMMDD', -- Format for date in invoice number
    `reset_frequency` ENUM('Daily', 'Monthly', 'Yearly', 'Never') DEFAULT 'Daily',
    `last_reset_date` DATE NULL,
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY `unique_sequence_type` (`sequence_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 9. STOCK_MOVEMENTS TABLE (Enhanced for Sales)
-- =====================================================
-- Tracks stock movements due to sales (extends existing stock management)
CREATE TABLE IF NOT EXISTS `stock_movements` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `product_id` INT NOT NULL,
    `movement_type` ENUM('Sale', 'Purchase', 'Adjustment', 'Return', 'Damage') NOT NULL,
    `reference_id` INT NULL, -- sale_id for sales, purchase_id for purchases
    `reference_type` ENUM('Sale', 'Purchase', 'Adjustment') NULL,
    `quantity_before` DECIMAL(8,3) NOT NULL,
    `quantity_changed` DECIMAL(8,3) NOT NULL, -- Positive for in, negative for out
    `quantity_after` DECIMAL(8,3) NOT NULL,
    `unit_price` DECIMAL(10,2) NULL,
    `total_value` DECIMAL(10,2) NULL,
    `notes` TEXT NULL,
    `created_by` VARCHAR(100) DEFAULT 'Admin',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
    INDEX `idx_stock_product` (`product_id`),
    INDEX `idx_stock_type` (`movement_type`),
    INDEX `idx_stock_reference` (`reference_id`, `reference_type`),
    INDEX `idx_stock_date` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default invoice sequence settings
INSERT INTO `invoice_sequences` (`sequence_type`, `prefix`, `current_number`, `date_format`, `reset_frequency`) VALUES
('Invoice', 'KGS', 1001, 'YYMMDD', 'Daily'),
('Bill', 'BILL', 1, 'YYMMDD', 'Daily'),
('Credit_Note', 'CN', 1, 'YYMMDD', 'Monthly')
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- USEFUL VIEWS FOR REPORTING
-- =====================================================

-- View for customer sales summary
CREATE OR REPLACE VIEW `customer_sales_summary` AS
SELECT 
    c.id,
    c.name,
    c.phone,
    c.category,
    c.credit_limit,
    c.current_credit,
    COUNT(s.id) as total_orders,
    COALESCE(SUM(s.grand_total), 0) as total_spent,
    COALESCE(AVG(s.grand_total), 0) as average_order_value,
    MAX(s.sale_date) as last_purchase_date
FROM customers c
LEFT JOIN sales s ON c.id = s.customer_id AND s.status = 'Completed'
GROUP BY c.id, c.name, c.phone, c.category, c.credit_limit, c.current_credit;

-- View for daily sales report
CREATE OR REPLACE VIEW `daily_sales_report` AS
SELECT 
    DATE(s.created_at) as sale_date,
    COUNT(*) as total_transactions,
    SUM(s.grand_total) as total_sales,
    SUM(s.total_gst) as total_gst,
    SUM(CASE WHEN s.payment_method = 'Cash' THEN s.grand_total ELSE 0 END) as cash_sales,
    SUM(CASE WHEN s.payment_method = 'UPI' THEN s.grand_total ELSE 0 END) as upi_sales,
    SUM(CASE WHEN s.payment_method = 'Credit' THEN s.grand_total ELSE 0 END) as credit_sales,
    COUNT(DISTINCT s.customer_id) as unique_customers
FROM sales s
WHERE s.status = 'Completed'
GROUP BY DATE(s.created_at)
ORDER BY sale_date DESC;

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Additional composite indexes for common queries
CREATE INDEX `idx_sales_date_status` ON `sales` (`sale_date`, `status`);
CREATE INDEX `idx_sales_customer_date` ON `sales` (`customer_id`, `sale_date`);
CREATE INDEX `idx_sale_items_product_date` ON `sale_items` (`product_id`, `created_at`);
CREATE INDEX `idx_credit_customer_status` ON `credit_transactions` (`customer_id`, `status`);

-- =====================================================
-- END OF SALES MODULE SCHEMA
-- =====================================================
