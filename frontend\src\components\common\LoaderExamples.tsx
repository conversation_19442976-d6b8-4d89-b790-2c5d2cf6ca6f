import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Loader, { 
  SpinnerLoader, 
  DotsLoader, 
  PulseLoader, 
  BarsLoader,
  TableLoader,
  CardLoader 
} from "@/components/common/GlobalLoader"
import { useLoader } from "@/hooks/useLoader"

/**
 * Example component showing different loader usage patterns
 * This is for demonstration purposes - you can delete this file
 */
export default function LoaderExamples() {
  const { isLoading, withLoading } = useLoader()
  const [showOverlay, setShowOverlay] = useState(false)
  const [showFullScreen, setShowFullScreen] = useState(false)

  const simulateApiCall = async () => {
    await withLoading(async () => {
      await new Promise(resolve => setTimeout(resolve, 2000))
    })
  }

  const showOverlayLoader = async () => {
    setShowOverlay(true)
    await new Promise(resolve => setTimeout(resolve, 3000))
    setShowOverlay(false)
  }

  const showFullScreenLoader = async () => {
    setShowFullScreen(true)
    await new Promise(resolve => setTimeout(resolve, 2000))
    setShowFullScreen(false)
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Loader Examples</h1>
      
      {/* Full Screen Loader */}
      {showFullScreen && (
        <Loader 
          fullScreen 
          text="Processing your request..." 
          size="xl" 
          variant="spinner"
        />
      )}

      {/* Basic Loaders */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Loader Variants</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center space-y-2">
              <SpinnerLoader size="lg" />
              <p className="text-sm">Spinner</p>
            </div>
            <div className="text-center space-y-2">
              <DotsLoader size="lg" />
              <p className="text-sm">Dots</p>
            </div>
            <div className="text-center space-y-2">
              <PulseLoader size="lg" />
              <p className="text-sm">Pulse</p>
            </div>
            <div className="text-center space-y-2">
              <BarsLoader size="lg" />
              <p className="text-sm">Bars</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sizes */}
      <Card>
        <CardHeader>
          <CardTitle>Different Sizes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="text-center space-y-2">
              <SpinnerLoader size="sm" />
              <p className="text-xs">Small</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader size="md" />
              <p className="text-xs">Medium</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader size="lg" />
              <p className="text-xs">Large</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader size="xl" />
              <p className="text-xs">Extra Large</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Different Colors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="text-center space-y-2">
              <SpinnerLoader color="primary" />
              <p className="text-xs">Primary</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader color="secondary" />
              <p className="text-xs">Secondary</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader color="accent" />
              <p className="text-xs">Accent</p>
            </div>
            <div className="text-center space-y-2">
              <SpinnerLoader color="muted" />
              <p className="text-xs">Muted</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={simulateApiCall} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader size="sm" className="mr-2" />
                  Loading...
                </>
              ) : (
                'Simulate API Call'
              )}
            </Button>
            
            <Button onClick={showOverlayLoader} variant="outline">
              Show Overlay Loader
            </Button>
            
            <Button onClick={showFullScreenLoader} variant="outline">
              Show Full Screen Loader
            </Button>
          </div>

          {/* Show loading state */}
          {isLoading && (
            <div className="p-4 border rounded-lg">
              <Loader text="API call in progress..." variant="dots" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Overlay Example */}
      <Card>
        <CardHeader>
          <CardTitle>Overlay Loader Example</CardTitle>
        </CardHeader>
        <CardContent className="relative min-h-[200px]">
          <p>This content will be overlaid with a loader when you click the button above.</p>
          <p>The overlay prevents interaction while loading.</p>
          
          {showOverlay && (
            <Loader 
              overlay 
              text="Loading overlay..." 
              variant="pulse" 
              size="lg"
            />
          )}
        </CardContent>
      </Card>

      {/* Table Loader */}
      <Card>
        <CardHeader>
          <CardTitle>Table Skeleton Loader</CardTitle>
        </CardHeader>
        <CardContent>
          <TableLoader rows={4} columns={5} />
        </CardContent>
      </Card>

      {/* Card Loader */}
      <Card>
        <CardHeader>
          <CardTitle>Card Skeleton Loader</CardTitle>
        </CardHeader>
        <CardContent>
          <CardLoader count={3} />
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Usage Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Simple Import & Use:</h4>
            <code className="text-sm">
              {`import Loader from "@/components/common/GlobalLoader"
              
{isLoading && <Loader />}`}
            </code>
          </div>
          
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-semibold mb-2">With Hook:</h4>
            <code className="text-sm">
              {`import { useLoader } from "@/hooks/useLoader"

const { isLoading, withLoading } = useLoader()

await withLoading(async () => {
  // Your API call
})`}
            </code>
          </div>
          
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Button with Loader:</h4>
            <code className="text-sm">
              {`<Button disabled={isLoading}>
  {isLoading ? (
    <><Loader size="sm" className="mr-2" />Loading...</>
  ) : (
    'Submit'
  )}
</Button>`}
            </code>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
