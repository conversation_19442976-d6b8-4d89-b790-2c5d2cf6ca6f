import { useEffect } from "react"
import { useLocation, useNavigate } from "react-router-dom"
import { useNavigation } from "@/hooks/useNavigation"

interface RouteGuardProps {
    children: React.ReactNode
}

export function RouteGuard({ children }: RouteGuardProps) {
    const location = useLocation()
    const navigate = useNavigate()
    const { isValidRoute } = useNavigation()

    useEffect(() => {
        // Check if current route is valid
        if (!isValidRoute(location.pathname)) {
            console.warn(`Invalid route detected: ${location.pathname}. Redirecting to dashboard.`)
            navigate("/", { replace: true })
        }
    }, [location.pathname, isValidRoute, navigate])

    // Only render children if route is valid
    if (!isValidRoute(location.pathname)) {
        return null // or a loading spinner
    }

    return <>{children}</>
}
