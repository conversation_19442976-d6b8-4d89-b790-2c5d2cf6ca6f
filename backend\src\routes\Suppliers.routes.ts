import { Router } from "express";
import { SuppliersController } from "../controllers/SuppliersController";

const router = Router();
const suppliersController = new SuppliersController();

router.get("/", suppliersController.allSuppliers.bind(suppliersController));
router.get("/:id", suppliersController.getSupplier.bind(suppliersController));
router.post("/", suppliersController.addSupplier.bind(suppliersController));
router.put("/:id", suppliersController.editSupplier.bind(suppliersController));
router.patch("/:id/status", suppliersController.toggleSupplierStatus.bind(suppliersController));
router.delete("/:id", suppliersController.deleteSupplier.bind(suppliersController));


export default router;