import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, Save, AlertCircle, CheckCircle } from "lucide-react"

// Mock data for the product
const mockProduct = {
    id: 1,
    name: "Basmati Rice 1kg",
    sku: "BR001",
    category: "Grains",
    unit: "kg",
    stock: 45,
    price: 120,
    lowStockThreshold: 10,
    description: "Premium quality Basmati rice, perfect for biryanis and pulao.",
    status: "Active"
}

const categories = ["Grains", "Spices", "Dairy", "Instant Food", "Snacks", "Beverages", "Cleaning", "Personal Care"]
const units = ["kg", "g", "l", "ml", "pack", "piece", "dozen", "box"]

export default function EditProduct() {
    const navigate = useNavigate()
    const { id } = useParams()
    const [isLoading, setIsLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)
    const [errors, setErrors] = useState<Record<string, string>>({})
    
    const [formData, setFormData] = useState({
        name: "",
        sku: "",
        category: "",
        unit: "",
        stock: "",
        price: "",
        lowStockThreshold: "",
        description: "",
        status: "Active"
    })

    // Load product data on component mount
    useEffect(() => {
        // In a real app, you would fetch the product by ID from an API
        if (id) {
            setFormData({
                name: mockProduct.name,
                sku: mockProduct.sku,
                category: mockProduct.category,
                unit: mockProduct.unit,
                stock: mockProduct.stock.toString(),
                price: mockProduct.price.toString(),
                lowStockThreshold: mockProduct.lowStockThreshold.toString(),
                description: mockProduct.description,
                status: mockProduct.status
            })
        }
    }, [id])

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: ""
            }))
        }
    }

    const validateForm = () => {
        const newErrors: Record<string, string> = {}

        if (!formData.name.trim()) {
            newErrors.name = "Product name is required"
        }

        if (!formData.sku.trim()) {
            newErrors.sku = "SKU is required"
        }

        if (!formData.category) {
            newErrors.category = "Category is required"
        }

        if (!formData.unit) {
            newErrors.unit = "Unit is required"
        }

        if (!formData.stock.trim()) {
            newErrors.stock = "Stock quantity is required"
        } else if (isNaN(Number(formData.stock)) || Number(formData.stock) < 0) {
            newErrors.stock = "Stock must be a valid number"
        }

        if (!formData.price.trim()) {
            newErrors.price = "Price is required"
        } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
            newErrors.price = "Price must be a valid positive number"
        }

        if (!formData.lowStockThreshold.trim()) {
            newErrors.lowStockThreshold = "Low stock threshold is required"
        } else if (isNaN(Number(formData.lowStockThreshold)) || Number(formData.lowStockThreshold) < 0) {
            newErrors.lowStockThreshold = "Low stock threshold must be a valid number"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        
        if (!validateForm()) {
            return
        }

        setIsLoading(true)

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500))
            
            console.log("Updated product:", formData)
            setShowSuccess(true)
            
            // Hide success message after 3 seconds and navigate back
            setTimeout(() => {
                setShowSuccess(false)
                navigate("/inventory/products")
            }, 2000)
            
        } catch (error) {
            console.error("Error updating product:", error)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate("/inventory/products")}
                >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Products
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Edit Product</h1>
                    <p className="text-muted-foreground">
                        Update product information and inventory details.
                    </p>
                </div>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                        Product updated successfully! Redirecting to product list...
                    </AlertDescription>
                </Alert>
            )}

            <div className="grid gap-6 md:grid-cols-2">
                {/* Product Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Product Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Product Name *</Label>
                            <Input
                                id="name"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                placeholder="Enter product name"
                                className={errors.name ? "border-red-500" : ""}
                            />
                            {errors.name && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.name}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="sku">SKU *</Label>
                            <Input
                                id="sku"
                                value={formData.sku}
                                onChange={(e) => handleInputChange("sku", e.target.value)}
                                placeholder="Enter SKU"
                                className={errors.sku ? "border-red-500" : ""}
                            />
                            {errors.sku && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.sku}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="category">Category *</Label>
                            <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                                <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                                    <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.category && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.category}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="unit">Unit *</Label>
                            <Select value={formData.unit} onValueChange={(value) => handleInputChange("unit", value)}>
                                <SelectTrigger className={errors.unit ? "border-red-500" : ""}>
                                    <SelectValue placeholder="Select unit" />
                                </SelectTrigger>
                                <SelectContent>
                                    {units.map((unit) => (
                                        <SelectItem key={unit} value={unit}>
                                            {unit}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.unit && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.unit}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                                placeholder="Enter product description"
                                rows={3}
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Inventory & Pricing */}
                <Card>
                    <CardHeader>
                        <CardTitle>Inventory & Pricing</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="stock">Current Stock *</Label>
                            <Input
                                id="stock"
                                type="number"
                                value={formData.stock}
                                onChange={(e) => handleInputChange("stock", e.target.value)}
                                placeholder="Enter current stock"
                                min="0"
                                className={errors.stock ? "border-red-500" : ""}
                            />
                            {errors.stock && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.stock}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="price">Price (₹) *</Label>
                            <Input
                                id="price"
                                type="number"
                                value={formData.price}
                                onChange={(e) => handleInputChange("price", e.target.value)}
                                placeholder="Enter price"
                                min="0"
                                step="0.01"
                                className={errors.price ? "border-red-500" : ""}
                            />
                            {errors.price && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.price}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="lowStockThreshold">Low Stock Threshold *</Label>
                            <Input
                                id="lowStockThreshold"
                                type="number"
                                value={formData.lowStockThreshold}
                                onChange={(e) => handleInputChange("lowStockThreshold", e.target.value)}
                                placeholder="Enter low stock threshold"
                                min="0"
                                className={errors.lowStockThreshold ? "border-red-500" : ""}
                            />
                            {errors.lowStockThreshold && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.lowStockThreshold}
                                </p>
                            )}
                            <p className="text-xs text-muted-foreground">
                                You'll be alerted when stock falls below this level
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="status">Status</Label>
                            <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Active">Active</SelectItem>
                                    <SelectItem value="Inactive">Inactive</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="pt-4">
                            <Button
                                type="submit"
                                onClick={handleSubmit}
                                disabled={isLoading}
                                className="w-full"
                            >
                                {isLoading ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Updating Product...
                                    </>
                                ) : (
                                    <>
                                        <Save className="h-4 w-4 mr-2" />
                                        Update Product
                                    </>
                                )}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}
