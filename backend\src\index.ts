import dotenv from "dotenv";
import { AppDataSource } from "./dbconfig";
import express from "express";
import cors from "cors";
import apiRoutes from "./routes/index.routes";

dotenv.config();

const app = express();

// CORS configuration
const corsOptions = {
    origin: [
        'http://localhost:5173', // Vite default port
        'http://localhost:5174', // Vite alternative port
        'http://localhost:3000', // React default port (if needed)
        'http://127.0.0.1:5173',
        'http://127.0.0.1:5174',
        'http://127.0.0.1:3000'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ],
    credentials: true, // Allow cookies and credentials
    optionsSuccessStatus: 200 // For legacy browser support
};

// Global middlewares
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use("/api", apiRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Simple API endpoint
// app.get('/api', (req, res) => {
//     res.json({
//         message: 'Kirana Shop API',
//         version: '1.0.0',
//         timestamp: new Date().toISOString()
//     });
// });

// 404 handler for undefined routes
app.use((req, res) => {
    res.status(404).json({
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
    });
});

AppDataSource.initialize()
    .then(() => {
        console.log('📦 Connected to DB');
        const PORT = process.env.PORT || 5000;
        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`📋 Health check available at http://localhost:${PORT}/health`);
        });
    })
    .catch((error) => console.error('❌ DB Connection Error: ', error));