import { TrendingUp, TrendingDown, AlertTriangle } from "lucide-react"
import type { LucideIcon } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface MetricCardProps {
    title: string
    value: string
    icon: LucideIcon
    trend?: {
        direction: 'up' | 'down'
        percentage: number
        label?: string
    }
    alert?: {
        message: string
        type: 'warning' | 'info'
    }
    className?: string
}

export function MetricCard({ 
    title, 
    value, 
    icon: Icon, 
    trend, 
    alert, 
    className = "" 
}: MetricCardProps) {
    return (
        <Card className={`${className}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                    {title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
                
                {/* Trend indicator */}
                {trend && (
                    <div className="flex items-center gap-1 mt-2">
                        {trend.direction === 'up' ? (
                            <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                            <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                        <span className={`text-sm ${
                            trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                            {trend.percentage}%
                        </span>
                        {trend.label && (
                            <span className="text-sm text-muted-foreground">
                                {trend.label}
                            </span>
                        )}
                    </div>
                )}

                {/* Alert message */}
                {alert && (
                    <div className="flex items-center gap-1 mt-2">
                        <AlertTriangle className={`h-4 w-4 ${
                            alert.type === 'warning' ? 'text-orange-500' : 'text-blue-500'
                        }`} />
                        <span className={`text-sm ${
                            alert.type === 'warning' ? 'text-orange-600' : 'text-blue-600'
                        }`}>
                            {alert.message}
                        </span>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
