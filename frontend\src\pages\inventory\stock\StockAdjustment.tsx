import { useState } from "react"
import { useNavi<PERSON> } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
    ArrowLeft,
    Plus,
    Minus,
    Save,
    Trash2,
    Package,
    CheckCircle,
    AlertCircle,
    TrendingUp,
    TrendingDown
} from "lucide-react"

// Mock data for products
const mockProducts = [
    { id: 1, name: "Basmati Rice 1kg", sku: "BR001", currentStock: 45, unit: "kg" },
    { id: 2, name: "Tata Salt 1kg", sku: "TS001", currentStock: 8, unit: "kg" },
    { id: 3, name: "Amul Milk 500ml", sku: "AM001", currentStock: 0, unit: "ml" },
    { id: 4, name: "Maggi Noodles", sku: "MN001", currentStock: 67, unit: "pack" },
    { id: 5, name: "Britannia Biscuits", sku: "BB001", currentStock: 12, unit: "pack" }
]

interface AdjustmentItem {
    id: string
    productId: number
    product: any
    type: "in" | "out" | "adjust"
    quantity: number
    reason: string
}

export default function StockAdjustment() {
    const navigate = useNavigate()
    const [adjustments, setAdjustments] = useState<AdjustmentItem[]>([])
    const [selectedProductId, setSelectedProductId] = useState<string>("")
    const [adjustmentType, setAdjustmentType] = useState<"in" | "out" | "adjust">("in")
    const [quantity, setQuantity] = useState("")
    const [reason, setReason] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [showSuccess, setShowSuccess] = useState(false)
    const [errors, setErrors] = useState<Record<string, string>>({})

    const addAdjustment = () => {
        // Validate form
        const newErrors: Record<string, string> = {}
        
        if (!selectedProductId) {
            newErrors.product = "Please select a product"
        }
        
        if (!quantity || isNaN(Number(quantity)) || Number(quantity) <= 0) {
            newErrors.quantity = "Please enter a valid quantity"
        }
        
        if (!reason.trim()) {
            newErrors.reason = "Please provide a reason"
        }

        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors)
            return
        }

        const product = mockProducts.find(p => p.id === Number(selectedProductId))
        if (!product) return

        const newAdjustment: AdjustmentItem = {
            id: Date.now().toString(),
            productId: product.id,
            product,
            type: adjustmentType,
            quantity: Number(quantity),
            reason: reason.trim()
        }

        setAdjustments([...adjustments, newAdjustment])
        
        // Reset form
        setSelectedProductId("")
        setQuantity("")
        setReason("")
        setErrors({})
    }

    const removeAdjustment = (id: string) => {
        setAdjustments(adjustments.filter(adj => adj.id !== id))
    }

    const calculateNewStock = (adjustment: AdjustmentItem) => {
        const currentStock = adjustment.product.currentStock
        switch (adjustment.type) {
            case "in":
                return currentStock + adjustment.quantity
            case "out":
                return Math.max(0, currentStock - adjustment.quantity)
            case "adjust":
                return adjustment.quantity
            default:
                return currentStock
        }
    }

    const handleSubmit = async () => {
        if (adjustments.length === 0) {
            setErrors({ submit: "Please add at least one adjustment" })
            return
        }

        setIsLoading(true)
        setErrors({})

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000))
            
            console.log("Stock adjustments:", adjustments)
            setShowSuccess(true)
            
            // Clear adjustments and show success
            setTimeout(() => {
                setAdjustments([])
                setShowSuccess(false)
                navigate("/inventory/stock")
            }, 2000)
            
        } catch (error) {
            console.error("Error processing adjustments:", error)
            setErrors({ submit: "Failed to process adjustments. Please try again." })
        } finally {
            setIsLoading(false)
        }
    }

    const getAdjustmentIcon = (type: string) => {
        switch (type) {
            case "in":
                return <TrendingUp className="h-4 w-4 text-green-600" />
            case "out":
                return <TrendingDown className="h-4 w-4 text-red-600" />
            case "adjust":
                return <Package className="h-4 w-4 text-blue-600" />
            default:
                return <Package className="h-4 w-4" />
        }
    }

    const getAdjustmentBadge = (type: string) => {
        switch (type) {
            case "in":
                return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Stock In</Badge>
            case "out":
                return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Stock Out</Badge>
            case "adjust":
                return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Adjust</Badge>
            default:
                return <Badge variant="secondary">{type}</Badge>
        }
    }

    // Filter out already selected products
    const availableProducts = mockProducts.filter(product => 
        !adjustments.some(adj => adj.productId === product.id)
    )

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate("/inventory/stock")}
                >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Stock Management
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Bulk Stock Adjustment</h1>
                    <p className="text-muted-foreground">
                        Adjust stock levels for multiple products at once.
                    </p>
                </div>
            </div>

            {/* Success Alert */}
            {showSuccess && (
                <Alert className="border-green-500/50 text-green-600 dark:border-green-500">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                        Stock adjustments processed successfully! Redirecting to stock management...
                    </AlertDescription>
                </Alert>
            )}

            {/* Error Alert */}
            {errors.submit && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errors.submit}</AlertDescription>
                </Alert>
            )}

            <div className="grid gap-6 lg:grid-cols-2">
                {/* Add Adjustment Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Add Stock Adjustment</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="product">Product *</Label>
                            <Select value={selectedProductId} onValueChange={setSelectedProductId}>
                                <SelectTrigger className={errors.product ? "border-red-500" : ""}>
                                    <SelectValue placeholder="Select a product" />
                                </SelectTrigger>
                                <SelectContent>
                                    {availableProducts.map((product) => (
                                        <SelectItem key={product.id} value={product.id.toString()}>
                                            {product.name} (SKU: {product.sku}) - Current: {product.currentStock} {product.unit}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.product && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.product}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="type">Adjustment Type *</Label>
                            <Select value={adjustmentType} onValueChange={(value: "in" | "out" | "adjust") => setAdjustmentType(value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="in">Stock In (Add to current stock)</SelectItem>
                                    <SelectItem value="out">Stock Out (Remove from current stock)</SelectItem>
                                    <SelectItem value="adjust">Set Exact Stock (Override current stock)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="quantity">
                                {adjustmentType === "adjust" ? "New Stock Quantity" : "Quantity"} *
                            </Label>
                            <Input
                                id="quantity"
                                type="number"
                                value={quantity}
                                onChange={(e) => setQuantity(e.target.value)}
                                placeholder={adjustmentType === "adjust" ? "Enter new stock quantity" : "Enter quantity to adjust"}
                                min="0"
                                className={errors.quantity ? "border-red-500" : ""}
                            />
                            {errors.quantity && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.quantity}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="reason">Reason *</Label>
                            <Textarea
                                id="reason"
                                value={reason}
                                onChange={(e) => setReason(e.target.value)}
                                placeholder="Enter reason for adjustment"
                                rows={3}
                                className={errors.reason ? "border-red-500" : ""}
                            />
                            {errors.reason && (
                                <p className="text-sm text-red-600 flex items-center gap-1">
                                    <AlertCircle className="h-3 w-3" />
                                    {errors.reason}
                                </p>
                            )}
                        </div>

                        <Button onClick={addAdjustment} className="w-full">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Adjustment
                        </Button>
                    </CardContent>
                </Card>

                {/* Adjustments Summary */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <span>Adjustments Summary</span>
                            <Badge variant="secondary">{adjustments.length} items</Badge>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {adjustments.length === 0 ? (
                            <div className="text-center py-8">
                                <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No adjustments added</h3>
                                <p className="text-muted-foreground">
                                    Add stock adjustments using the form on the left.
                                </p>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {adjustments.map((adjustment) => (
                                    <div key={adjustment.id} className="p-4 border rounded-lg">
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-2">
                                                    {getAdjustmentIcon(adjustment.type)}
                                                    <span className="font-medium">{adjustment.product.name}</span>
                                                    {getAdjustmentBadge(adjustment.type)}
                                                </div>
                                                <div className="text-sm text-muted-foreground space-y-1">
                                                    <p>SKU: {adjustment.product.sku}</p>
                                                    <p>Current Stock: {adjustment.product.currentStock} {adjustment.product.unit}</p>
                                                    <p>New Stock: <span className="font-medium">{calculateNewStock(adjustment)} {adjustment.product.unit}</span></p>
                                                    <p>Reason: {adjustment.reason}</p>
                                                </div>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeAdjustment(adjustment.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                                
                                <div className="pt-4 border-t">
                                    <Button
                                        onClick={handleSubmit}
                                        disabled={isLoading || adjustments.length === 0}
                                        className="w-full"
                                    >
                                        {isLoading ? (
                                            <>
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                Processing Adjustments...
                                            </>
                                        ) : (
                                            <>
                                                <Save className="h-4 w-4 mr-2" />
                                                Process All Adjustments
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Adjustments Table (when there are items) */}
            {adjustments.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle>Adjustment Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Product</TableHead>
                                    <TableHead>SKU</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Current Stock</TableHead>
                                    <TableHead>Adjustment</TableHead>
                                    <TableHead>New Stock</TableHead>
                                    <TableHead>Reason</TableHead>
                                    <TableHead>Action</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {adjustments.map((adjustment) => (
                                    <TableRow key={adjustment.id}>
                                        <TableCell className="font-medium">{adjustment.product.name}</TableCell>
                                        <TableCell className="text-muted-foreground">{adjustment.product.sku}</TableCell>
                                        <TableCell>{getAdjustmentBadge(adjustment.type)}</TableCell>
                                        <TableCell>{adjustment.product.currentStock} {adjustment.product.unit}</TableCell>
                                        <TableCell>
                                            <span className={`font-medium ${
                                                adjustment.type === "in" ? "text-green-600" : 
                                                adjustment.type === "out" ? "text-red-600" : "text-blue-600"
                                            }`}>
                                                {adjustment.type === "in" ? "+" : adjustment.type === "out" ? "-" : "="}{adjustment.quantity}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            <span className="font-medium">{calculateNewStock(adjustment)} {adjustment.product.unit}</span>
                                        </TableCell>
                                        <TableCell className="text-muted-foreground">{adjustment.reason}</TableCell>
                                        <TableCell>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeAdjustment(adjustment.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}
