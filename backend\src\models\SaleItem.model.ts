import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    Index,
} from "typeorm";
import { Sales } from "./Sales.model";
import { Product } from "./Product.model";

@Entity("sale_items")
export class SaleItem {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Sales, (sale) => sale.items, { onDelete: "CASCADE" })
    @JoinColumn({ name: "sale_id" })
    @Index("idx_sale_items_sale")
    sale!: Sales;

    @ManyToOne(() => Product, { onDelete: "RESTRICT" })
    @JoinColumn({ name: "product_id" })
    @Index("idx_sale_items_product")
    product!: Product;

    @Column({ type: "varchar", length: 255, name: "product_name" })
    productName!: string;

    @Column({ type: "varchar", length: 50, nullable: true, name: "product_barcode" })
    productBarcode?: string;

    @Column({ type: "varchar", length: 100, nullable: true, name: "category_name" })
    categoryName?: string;

    @Column({ type: "varchar", length: 50, nullable: true, name: "unit_name" })
    unitName?: string;

    @Column("decimal", { precision: 8, scale: 3 })
    quantity!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "unit_price" })
    unitPrice!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "total_price" })
    totalPrice!: number;

    @Column("decimal", { precision: 5, scale: 2, default: 0.00, name: "gst_rate" })
    gstRate!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "gst_amount" })
    gstAmount!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "discount_amount" })
    discountAmount!: number;

    @Column("decimal", { precision: 10, scale: 2, name: "final_amount" })
    finalAmount!: number;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    // Virtual properties for calculations
    get baseAmount(): number {
        return this.quantity * this.unitPrice;
    }

    get taxableAmount(): number {
        return this.baseAmount - this.discountAmount;
    }

    get effectiveGstRate(): number {
        return this.taxableAmount > 0 ? (this.gstAmount / this.taxableAmount) * 100 : 0;
    }

    get profitAmount(): number {
        // This would need product purchase price to calculate
        // For now, return 0 as placeholder
        return 0;
    }

    get profitPercentage(): number {
        return this.finalAmount > 0 ? (this.profitAmount / this.finalAmount) * 100 : 0;
    }
}
