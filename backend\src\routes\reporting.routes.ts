import { Router } from "express";
import { ReportingController } from "../controllers/ReportingController";

const router = Router();
const reportingController = new ReportingController();

// Analytics endpoints
router.get("/sales-analytics", reportingController.getSalesAnalytics);
router.get("/customer-analytics", reportingController.getCustomerAnalytics);
router.get("/product-performance", reportingController.getProductPerformance);

// Dashboard and summary endpoints
router.get("/dashboard-summary", reportingController.getDashboardSummary);
router.get("/monthly-comparison", reportingController.getMonthlyComparison);
router.get("/yearly-summary", reportingController.getYearlySummary);

// Export endpoints
router.get("/export/sales-report", reportingController.exportSalesReport);

export default router;
