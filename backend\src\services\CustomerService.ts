import { Repository, DataSource, Like } from "typeorm";
import { Customer } from "../models/Customer.model";
import { CreditTransaction } from "../models/CreditTransaction.model";
import { Sales } from "../models/Sales.model";

export interface CreateCustomerRequest {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    category?: "Regular" | "Wholesale" | "VIP";
    creditLimit?: number;
    gstNumber?: string;
}

export interface UpdateCustomerRequest {
    name?: string;
    phone?: string;
    email?: string;
    address?: string;
    category?: "Regular" | "Wholesale" | "VIP";
    creditLimit?: number;
    gstNumber?: string;
    isActive?: boolean;
}

export interface CustomerSearchFilters {
    name?: string;
    phone?: string;
    category?: "Regular" | "Wholesale" | "VIP";
    creditStatus?: "Good" | "Warning" | "Overdue";
    isActive?: boolean;
}

export interface CreditPaymentRequest {
    customerId: number;
    amount: number;
    paymentMethod: "Cash" | "UPI" | "Card";
    referenceNumber?: string;
    description?: string;
}

export class CustomerService {
    private customerRepository: Repository<Customer>;
    private creditTransactionRepository: Repository<CreditTransaction>;
    private salesRepository: Repository<Sales>;
    private dataSource: DataSource;

    constructor(dataSource: DataSource) {
        this.dataSource = dataSource;
        this.customerRepository = dataSource.getRepository(Customer);
        this.creditTransactionRepository = dataSource.getRepository(CreditTransaction);
        this.salesRepository = dataSource.getRepository(Sales);
    }

    async createCustomer(customerData: CreateCustomerRequest): Promise<Customer> {
        // Check if phone number already exists
        const existingCustomer = await this.customerRepository.findOne({
            where: { phone: customerData.phone }
        });

        if (existingCustomer) {
            throw new Error("Customer with this phone number already exists");
        }

        const customer = new Customer();
        customer.name = customerData.name;
        customer.phone = customerData.phone;
        customer.email = customerData.email;
        customer.address = customerData.address;
        customer.category = customerData.category || "Regular";
        customer.creditLimit = customerData.creditLimit || 0;
        customer.gstNumber = customerData.gstNumber;
        customer.isActive = true;

        return await this.customerRepository.save(customer);
    }

    async updateCustomer(id: number, updateData: UpdateCustomerRequest): Promise<Customer> {
        const customer = await this.customerRepository.findOne({ where: { id } });

        if (!customer) {
            throw new Error("Customer not found");
        }

        // Check if phone number is being changed and if it already exists
        if (updateData.phone && updateData.phone !== customer.phone) {
            const existingCustomer = await this.customerRepository.findOne({
                where: { phone: updateData.phone }
            });

            if (existingCustomer) {
                throw new Error("Customer with this phone number already exists");
            }
        }

        // Update fields
        Object.assign(customer, updateData);

        return await this.customerRepository.save(customer);
    }

    async getCustomerById(id: number): Promise<Customer | null> {
        return await this.customerRepository.findOne({
            where: { id },
            relations: ["sales", "creditTransactions"]
        });
    }

    async searchCustomers(
        filters: CustomerSearchFilters,
        page: number = 1,
        limit: number = 20
    ): Promise<{ customers: Customer[]; total: number; page: number; totalPages: number }> {
        const queryBuilder = this.customerRepository.createQueryBuilder("customer");

        // Apply filters
        if (filters.name) {
            queryBuilder.andWhere("customer.name LIKE :name", { name: `%${filters.name}%` });
        }

        if (filters.phone) {
            queryBuilder.andWhere("customer.phone LIKE :phone", { phone: `%${filters.phone}%` });
        }

        if (filters.category) {
            queryBuilder.andWhere("customer.category = :category", { category: filters.category });
        }

        if (filters.isActive !== undefined) {
            queryBuilder.andWhere("customer.isActive = :isActive", { isActive: filters.isActive });
        }

        // Credit status filter
        if (filters.creditStatus) {
            switch (filters.creditStatus) {
                case "Good":
                    queryBuilder.andWhere("(customer.currentCredit / customer.creditLimit) < 0.7");
                    break;
                case "Warning":
                    queryBuilder.andWhere("(customer.currentCredit / customer.creditLimit) >= 0.7 AND (customer.currentCredit / customer.creditLimit) < 0.9");
                    break;
                case "Overdue":
                    queryBuilder.andWhere("(customer.currentCredit / customer.creditLimit) >= 0.9");
                    break;
            }
        }

        queryBuilder.orderBy("customer.name", "ASC");

        const total = await queryBuilder.getCount();
        const customers = await queryBuilder
            .skip((page - 1) * limit)
            .take(limit)
            .getMany();

        return {
            customers,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getCustomersByPhone(phone: string): Promise<Customer[]> {
        return await this.customerRepository.find({
            where: {
                phone: Like(`%${phone}%`),
                isActive: true
            },
            take: 10
        });
    }

    async getCustomersByName(name: string): Promise<Customer[]> {
        return await this.customerRepository.find({
            where: {
                name: Like(`%${name}%`),
                isActive: true
            },
            take: 10
        });
    }

    async recordCreditPayment(paymentData: CreditPaymentRequest): Promise<CreditTransaction> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const customer = await queryRunner.manager.findOne(Customer, {
                where: { id: paymentData.customerId }
            });

            if (!customer) {
                throw new Error("Customer not found");
            }

            if (customer.currentCredit < paymentData.amount) {
                throw new Error("Payment amount exceeds current credit balance");
            }

            // Create credit transaction
            const creditTransaction = new CreditTransaction();
            creditTransaction.customer = customer;
            creditTransaction.transactionType = "Payment_Received";
            creditTransaction.amount = paymentData.amount;
            creditTransaction.balanceBefore = customer.currentCredit;
            creditTransaction.balanceAfter = customer.currentCredit - paymentData.amount;
            creditTransaction.paymentMethod = paymentData.paymentMethod;
            creditTransaction.referenceNumber = paymentData.referenceNumber;
            creditTransaction.description = paymentData.description || `Payment received via ${paymentData.paymentMethod}`;
            creditTransaction.status = "Paid";

            await queryRunner.manager.save(CreditTransaction, creditTransaction);

            // Update customer credit balance
            customer.currentCredit -= paymentData.amount;
            await queryRunner.manager.save(Customer, customer);

            await queryRunner.commitTransaction();
            return creditTransaction;

        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    async getCustomerCreditHistory(
        customerId: number,
        page: number = 1,
        limit: number = 20
    ): Promise<{ transactions: CreditTransaction[]; total: number; page: number; totalPages: number }> {
        const [transactions, total] = await this.creditTransactionRepository.findAndCount({
            where: { customer: { id: customerId } },
            relations: ["sale"],
            order: { createdAt: "DESC" },
            skip: (page - 1) * limit,
            take: limit
        });

        return {
            transactions,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getOverdueCustomers(): Promise<Customer[]> {
        const queryBuilder = this.customerRepository.createQueryBuilder("customer")
            .leftJoinAndSelect("customer.creditTransactions", "creditTransaction")
            .where("customer.currentCredit > 0")
            .andWhere("customer.isActive = true")
            .andWhere("creditTransaction.dueDate < :today", { today: new Date() })
            .andWhere("creditTransaction.status = 'Active'")
            .orderBy("customer.currentCredit", "DESC");

        return await queryBuilder.getMany();
    }

    async getCustomerSalesHistory(
        customerId: number,
        page: number = 1,
        limit: number = 20
    ): Promise<{ sales: Sales[]; total: number; page: number; totalPages: number }> {
        const [sales, total] = await this.salesRepository.findAndCount({
            where: { customer: { id: customerId } },
            relations: ["items", "items.product"],
            order: { createdAt: "DESC" },
            skip: (page - 1) * limit,
            take: limit
        });

        return {
            sales,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async deleteCustomer(id: number): Promise<void> {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ["sales", "creditTransactions"]
        });

        if (!customer) {
            throw new Error("Customer not found");
        }

        // Check if customer has active sales or credit
        if (customer.sales && customer.sales.length > 0) {
            throw new Error("Cannot delete customer with existing sales records");
        }

        if (customer.currentCredit > 0) {
            throw new Error("Cannot delete customer with outstanding credit balance");
        }

        // Soft delete by marking as inactive
        customer.isActive = false;
        await this.customerRepository.save(customer);
    }

    async getCustomerStatistics(): Promise<{
        totalCustomers: number;
        activeCustomers: number;
        categorySummary: Record<string, number>;
        totalCreditOutstanding: number;
        overdueCustomers: number;
    }> {
        const totalCustomers = await this.customerRepository.count();
        const activeCustomers = await this.customerRepository.count({ where: { isActive: true } });

        // Category summary
        const categoryQuery = await this.customerRepository
            .createQueryBuilder("customer")
            .select("customer.category", "category")
            .addSelect("COUNT(*)", "count")
            .where("customer.isActive = true")
            .groupBy("customer.category")
            .getRawMany();

        const categorySummary = categoryQuery.reduce((acc, item) => {
            acc[item.category] = parseInt(item.count);
            return acc;
        }, {} as Record<string, number>);

        // Credit statistics
        const creditStats = await this.customerRepository
            .createQueryBuilder("customer")
            .select("SUM(customer.currentCredit)", "totalCredit")
            .addSelect("COUNT(CASE WHEN customer.currentCredit > 0 THEN 1 END)", "overdueCount")
            .where("customer.isActive = true")
            .getRawOne();

        return {
            totalCustomers,
            activeCustomers,
            categorySummary,
            totalCreditOutstanding: parseFloat(creditStats.totalCredit) || 0,
            overdueCustomers: parseInt(creditStats.overdueCount) || 0
        };
    }
}
