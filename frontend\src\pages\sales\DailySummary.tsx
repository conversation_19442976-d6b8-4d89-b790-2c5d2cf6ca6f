import { useState } from "react"
import { useNavigate } from "react-router-dom"
import {
    ArrowLeft,
    Calendar,
    IndianRupee,
    FileText,
    Users,
    TrendingUp,
    TrendingDown,
    CreditCard,
    Smartphone,
    Banknote,
    Eye,
    Download,
    RefreshCw
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MetricCard } from "@/components/dashboard/MetricCard"
import { todaysSales, getTodaysSalesTotal, getTodaysSalesCount } from "@/data/sampleTransactions"

export default function DailySummary() {
    const navigate = useNavigate()
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])

    // Calculate daily metrics
    const todaysSalesTotal = getTodaysSalesTotal()
    const todaysSalesCount = getTodaysSalesCount()
    const averageOrderValue = todaysSalesCount > 0 ? todaysSalesTotal / todaysSalesCount : 0

    // Calculate payment method breakdown
    const paymentBreakdown = todaysSales.reduce((acc, sale) => {
        acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.total
        return acc
    }, {} as Record<string, number>)

    // Calculate hourly sales distribution
    const hourlySales = todaysSales.reduce((acc, sale) => {
        const hour = new Date(sale.timestamp).getHours()
        const hourKey = `${hour}:00`
        acc[hourKey] = (acc[hourKey] || 0) + sale.total
        return acc
    }, {} as Record<string, number>)

    // Top selling products today
    const productSales = new Map()
    todaysSales.forEach(sale => {
        sale.items.forEach(item => {
            const current = productSales.get(item.productId) || { 
                name: item.productName, 
                quantity: 0, 
                revenue: 0 
            }
            current.quantity += item.quantity
            current.revenue += item.total
            productSales.set(item.productId, current)
        })
    })
    const topProducts = Array.from(productSales.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5)

    // Customer analysis
    const uniqueCustomers = new Set(todaysSales.map(sale => sale.customerId).filter(Boolean)).size
    const walkInSales = todaysSales.filter(sale => !sale.customerId).length

    return (
        <div className="flex flex-1 flex-col gap-4 sm:gap-6 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate("/sales")}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Sales
                    </Button>
                    <div>
                        <h1 className="text-2xl sm:text-3xl font-bold">Daily Summary</h1>
                        <p className="text-muted-foreground">
                            Detailed overview of today's sales performance
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <Badge variant="outline" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(selectedDate).toLocaleDateString('en-IN', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        })}
                    </Badge>
                    <Button variant="outline" size="sm">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    title="Total Sales"
                    value={`₹${todaysSalesTotal.toLocaleString('en-IN')}`}
                    icon={IndianRupee}
                    trend={{ direction: "up", percentage: 12, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Total Bills"
                    value={todaysSalesCount.toString()}
                    icon={FileText}
                    trend={{ direction: "up", percentage: 8, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Average Order"
                    value={`₹${averageOrderValue.toLocaleString('en-IN')}`}
                    icon={TrendingUp}
                    trend={{ direction: "up", percentage: 5, label: "vs yesterday" }}
                />
                <MetricCard
                    title="Customers Served"
                    value={(uniqueCustomers + walkInSales).toString()}
                    icon={Users}
                    trend={{ direction: "up", percentage: 3, label: "vs yesterday" }}
                />
            </div>

            {/* Main Content Grid */}
            <div className="grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Payment Methods Breakdown */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5" />
                            Payment Methods
                        </CardTitle>
                        <CardDescription>Revenue breakdown by payment type</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Banknote className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Cash</span>
                                </div>
                                <div className="text-right">
                                    <p className="font-medium">₹{(paymentBreakdown.Cash || 0).toLocaleString('en-IN')}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {todaysSalesTotal > 0 ? Math.round(((paymentBreakdown.Cash || 0) / todaysSalesTotal) * 100) : 0}%
                                    </p>
                                </div>
                            </div>
                            <Separator />
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Smartphone className="h-4 w-4 text-blue-600" />
                                    <span className="text-sm">UPI</span>
                                </div>
                                <div className="text-right">
                                    <p className="font-medium">₹{(paymentBreakdown.UPI || 0).toLocaleString('en-IN')}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {todaysSalesTotal > 0 ? Math.round(((paymentBreakdown.UPI || 0) / todaysSalesTotal) * 100) : 0}%
                                    </p>
                                </div>
                            </div>
                            <Separator />
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <CreditCard className="h-4 w-4 text-orange-600" />
                                    <span className="text-sm">Credit</span>
                                </div>
                                <div className="text-right">
                                    <p className="font-medium">₹{(paymentBreakdown.Credit || 0).toLocaleString('en-IN')}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {todaysSalesTotal > 0 ? Math.round(((paymentBreakdown.Credit || 0) / todaysSalesTotal) * 100) : 0}%
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Top Products */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="h-5 w-5" />
                            Top Products
                        </CardTitle>
                        <CardDescription>Best performing products today</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {topProducts.length > 0 ? (
                                topProducts.map((product, index) => (
                                    <div key={index} className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                                                {index + 1}
                                            </Badge>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">{product.name}</p>
                                                <p className="text-xs text-muted-foreground">Qty: {product.quantity}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium">₹{product.revenue.toLocaleString('en-IN')}</p>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <p className="text-center text-muted-foreground py-4">No sales data available</p>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Customer Analysis */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Customer Analysis
                        </CardTitle>
                        <CardDescription>Customer breakdown for today</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm">Registered Customers</span>
                                <div className="text-right">
                                    <p className="font-medium">{uniqueCustomers}</p>
                                    <p className="text-xs text-muted-foreground">
                                        ₹{todaysSales.filter(sale => sale.customerId).reduce((sum, sale) => sum + sale.total, 0).toLocaleString('en-IN')}
                                    </p>
                                </div>
                            </div>
                            <Separator />
                            <div className="flex items-center justify-between">
                                <span className="text-sm">Walk-in Customers</span>
                                <div className="text-right">
                                    <p className="font-medium">{walkInSales}</p>
                                    <p className="text-xs text-muted-foreground">
                                        ₹{todaysSales.filter(sale => !sale.customerId).reduce((sum, sale) => sum + sale.total, 0).toLocaleString('en-IN')}
                                    </p>
                                </div>
                            </div>
                            <Separator />
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Total Served</span>
                                <div className="text-right">
                                    <p className="font-medium">{uniqueCustomers + walkInSales}</p>
                                    <p className="text-xs text-muted-foreground">customers</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Recent Transactions */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Recent Transactions</CardTitle>
                        <CardDescription>Latest sales from today</CardDescription>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => navigate("/sales/history")}>
                        <Eye className="h-4 w-4 mr-2" />
                        View All
                    </Button>
                </CardHeader>
                <CardContent>
                    <div className="space-y-3">
                        {todaysSales.slice(0, 8).map((sale) => (
                            <div
                                key={sale.id}
                                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                onClick={() => navigate(`/sales/details/${sale.id}`)}
                            >
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <FileText className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="font-medium">{sale.billNumber}</p>
                                        <p className="text-sm text-muted-foreground">
                                            {sale.customerName || "Walk-in Customer"} • {sale.items.length} items
                                        </p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <p className="font-medium">₹{sale.total.toLocaleString('en-IN')}</p>
                                    <div className="flex items-center gap-2">
                                        <Badge variant={
                                            sale.paymentMethod === "Cash" ? "default" :
                                            sale.paymentMethod === "UPI" ? "secondary" : "outline"
                                        } className="text-xs">
                                            {sale.paymentMethod}
                                        </Badge>
                                        <span className="text-xs text-muted-foreground">
                                            {new Date(sale.timestamp).toLocaleTimeString('en-IN', {
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                        {todaysSales.length === 0 && (
                            <div className="text-center py-8">
                                <FileText className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
                                <p className="text-muted-foreground">No sales recorded today</p>
                                <Button 
                                    variant="outline" 
                                    className="mt-4"
                                    onClick={() => navigate("/sales/new")}
                                >
                                    Create First Sale
                                </Button>
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
