import { DataSource, Repository } from "typeorm";
import { Suppliers } from "../models/Suppliers.model";

export interface CreateSupplierRequest {
    name: string;
    phone: string;
    address?: string;
    status?: boolean;
    gstNumber?: string
}


export class SupplierService {

    private supplierRepository: Repository<Suppliers>;

    constructor(dataSource: DataSource) {
        this.supplierRepository = dataSource.getRepository(Suppliers);
    }

    // fetch all suppliers data
    async getAllSuppliers(): Promise<Suppliers[]> {
        return await this.supplierRepository.find();
    }

    // fetch supplier data by id
    async getSupplierById(id: number): Promise<Suppliers | null> {
        return await this.supplierRepository.findOne({ where: { id } });
    }

    // add a new supplier 
    async addSupplier(supplier: Suppliers): Promise<Suppliers> {
        // Check if supplier with same phone number already exists
        const existingSupplier = await this.supplierExists({ phone: supplier.phone }, false);
        if (existingSupplier) {
            throw new Error("A supplier with this phone number already exists. Please enter a different number.");
        }

        // Create and save the new supplier
        const newSupplier = this.supplierRepository.create(supplier);
        return await this.supplierRepository.save(newSupplier);
    }

    // update a existing supplier
    async updateSupplier(id: number, updateData: Partial<Suppliers>): Promise<Suppliers> {

        // check supplier exists or not 
        const existingSupplier = await this.supplierExists({ id }, true);
        if (!existingSupplier) {
            throw new Error("Supplier not found");
        }

        // If phone is being updated, check for duplicates
        if (updateData.phone && updateData.phone !== (existingSupplier as Suppliers).phone) {
            const duplicatePhone = await this.supplierRepository.findOne({
                where: { phone: updateData.phone }
            });
            if (duplicatePhone) {
                throw new Error("A supplier with this phone number already exists. Please enter a different number.");
            }
        }

        // If GST number is being updated, check if it exists
        if (updateData.gstNumber && updateData.gstNumber !== (existingSupplier as Suppliers).gstNumber) {
            const duplicateGst = await this.supplierRepository.findOne({
                where: { gstNumber: updateData.gstNumber }
            });
            if (duplicateGst) {
                throw new Error("A supplier with this GST number already exists. Please enter a different number.");
            }
        }

        // Update and save the supplier
        Object.assign(existingSupplier, updateData);
        return await this.supplierRepository.save(existingSupplier as Suppliers);

    }

    // delete a existing supplier
    async deleteSupplier(id: number): Promise<void> {

        // check if supplier with this id exists or not 
        const existingSupplier = await this.supplierExists({ id }, false);
        if (!existingSupplier) {
            throw new Error("Supplier not found");
        }
        // delete the supplier
        await this.supplierRepository.delete(id);

    }

    // toggle supplier status
    async supplierStatus(id: number): Promise<Suppliers> {

        // check if supplier exists with this id 
        const existingSupplier = await this.supplierExists({ id }, true) as Suppliers;
        if (!existingSupplier) {
            throw new Error("Supplier not Found");
        }

        // toggle the status 
        existingSupplier.status = existingSupplier.status === 1 ? 0 : 1;

        return await this.supplierRepository.save(existingSupplier);

    }

    // search supplier exists or not its a private function 
    private async supplierExists(supplier: Partial<Suppliers>, fetchSupplier: boolean = false): Promise<boolean | Suppliers | null> {
        const existingSupplier = await this.supplierRepository.findOne({ where: supplier });
        return fetchSupplier ? existingSupplier : !!existingSupplier;
    }

}
