import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    <PERSON><PERSON><PERSON><PERSON><PERSON>n,
    CreateDateColumn,
} from "typeorm";
import { Product } from "./Product.model";

export type StockChangeType = "IN" | "OUT" | "ADJUST";

@Entity("stock_logs")
export class StockLog {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Product)
    @JoinColumn({ name: "product_id" })
    product!: Product;

    @Column({
        type: "enum",
        enum: ["IN", "OUT", "ADJUST"],
    })
    changeType!: StockChangeType;

    @Column()
    quantity!: number;

    @Column({ type: "text", nullable: true })
    desc!: string;

    @Column({ type: "varchar", length: 50, nullable: true })
    source!: string;

    @Column({ type: "int", nullable: true })
    ref_id!: number;

    @CreateDateColumn({ type: "datetime", name: "datetime", default: () => "CURRENT_TIMESTAMP" })
    datetime!: Date;
}
