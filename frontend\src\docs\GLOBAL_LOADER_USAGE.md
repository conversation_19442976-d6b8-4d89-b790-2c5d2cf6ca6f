# Global Loader Component - Usage Guide

## 🎯 **Overview**

The Global Loader component provides a flexible, reusable loading indicator system for your Kirana Shop project. It includes multiple variants, sizes, and usage patterns to fit different scenarios.

---

## 📦 **Components Available**

### **1. Main Loader Component**
- `Loader` - Main configurable loader component
- `SpinnerLoader` - Spinner variant (default)
- `DotsLoader` - Animated dots variant
- `PulseLoader` - Pulsing circle variant
- `BarsLoader` - Animated bars variant

### **2. Specialized Loaders**
- `TableLoader` - Skeleton loader for tables/lists
- `CardLoader` - Skeleton loader for card layouts

### **3. Loading Hook**
- `useLoader` - Hook for managing loading states

---

## 🚀 **Basic Usage**

### **Simple Import and Use**

```tsx
import Loader from "@/components/common/GlobalLoader"

function MyComponent() {
  const [isLoading, setIsLoading] = useState(false)

  return (
    <div>
      {isLoading && <Loader />}
      {/* Your content */}
    </div>
  )
}
```

### **With Loading Hook**

```tsx
import Loader from "@/components/common/GlobalLoader"
import { useLoader } from "@/hooks/useLoader"

function MyComponent() {
  const { isLoading, withLoading } = useLoader()

  const fetchData = async () => {
    await withLoading(async () => {
      // Your API call
      const response = await fetch('/api/data')
      const data = await response.json()
      return data
    })
  }

  return (
    <div>
      {isLoading && <Loader text="Loading data..." />}
      <button onClick={fetchData}>Load Data</button>
    </div>
  )
}
```

---

## 🎨 **Loader Variants**

### **1. Spinner Loader (Default)**
```tsx
import { SpinnerLoader } from "@/components/common/GlobalLoader"

<SpinnerLoader size="md" color="primary" />
```

### **2. Dots Loader**
```tsx
import { DotsLoader } from "@/components/common/GlobalLoader"

<DotsLoader size="lg" color="accent" />
```

### **3. Pulse Loader**
```tsx
import { PulseLoader } from "@/components/common/GlobalLoader"

<PulseLoader size="xl" color="secondary" />
```

### **4. Bars Loader**
```tsx
import { BarsLoader } from "@/components/common/GlobalLoader"

<BarsLoader size="md" color="primary" />
```

---

## 📏 **Sizes and Colors**

### **Sizes**
- `sm` - Small (16px)
- `md` - Medium (24px) - Default
- `lg` - Large (32px)
- `xl` - Extra Large (48px)

### **Colors**
- `primary` - Primary theme color - Default
- `secondary` - Secondary theme color
- `accent` - Accent theme color
- `muted` - Muted/gray color

```tsx
<Loader size="lg" color="accent" />
```

---

## 🎭 **Display Modes**

### **1. Inline Loader**
```tsx
<Loader /> // Default inline display
```

### **2. Overlay Loader**
```tsx
<div className="relative">
  <YourContent />
  <Loader overlay text="Loading..." />
</div>
```

### **3. Full Screen Loader**
```tsx
<Loader fullScreen text="Please wait..." size="xl" />
```

---

## 📋 **List/Table Usage Examples**

### **Purchase List with Loader**

```tsx
import Loader, { TableLoader } from "@/components/common/GlobalLoader"
import { useLoader } from "@/hooks/useLoader"

function PurchaseList() {
  const { isLoading, withLoading } = useLoader()
  const [purchases, setPurchases] = useState([])

  const fetchPurchases = async () => {
    await withLoading(async () => {
      const response = await fetch('/api/purchases')
      const data = await response.json()
      setPurchases(data)
    })
  }

  useEffect(() => {
    fetchPurchases()
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Purchase List</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <TableLoader rows={5} columns={6} />
        ) : (
          <Table>
            {/* Your table content */}
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
```

### **Dashboard Cards with Loader**

```tsx
import { CardLoader } from "@/components/common/GlobalLoader"
import { useLoader } from "@/hooks/useLoader"

function Dashboard() {
  const { isLoading } = useLoader(true) // Start with loading

  return (
    <div>
      {isLoading ? (
        <CardLoader count={4} />
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Your dashboard cards */}
        </div>
      )}
    </div>
  )
}
```

---

## 🔄 **Form Submission with Loader**

```tsx
import Loader from "@/components/common/GlobalLoader"
import { useLoader } from "@/hooks/useLoader"

function AddPurchaseForm() {
  const { isLoading, withLoading } = useLoader()

  const handleSubmit = async (formData) => {
    await withLoading(async () => {
      const response = await fetch('/api/purchases', {
        method: 'POST',
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        // Success handling
        navigate('/purchases')
      }
    })
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      
      <Button type="submit" disabled={isLoading}>
        {isLoading ? (
          <>
            <Loader size="sm" className="mr-2" />
            Saving...
          </>
        ) : (
          'Save Purchase'
        )}
      </Button>
    </form>
  )
}
```

---

## 🎯 **Advanced Usage Patterns**

### **Conditional Loading with Different Messages**

```tsx
function DataComponent() {
  const [loadingState, setLoadingState] = useState('')

  const loadData = async () => {
    setLoadingState('Fetching data...')
    // Fetch data
    
    setLoadingState('Processing...')
    // Process data
    
    setLoadingState('Almost done...')
    // Final steps
    
    setLoadingState('')
  }

  return (
    <div>
      {loadingState && (
        <Loader 
          text={loadingState} 
          variant="dots" 
          size="lg" 
        />
      )}
    </div>
  )
}
```

### **Multiple Loading States**

```tsx
function ComplexComponent() {
  const dataLoader = useLoader()
  const saveLoader = useLoader()
  const deleteLoader = useLoader()

  return (
    <div>
      {/* Data loading */}
      {dataLoader.isLoading && (
        <Loader overlay text="Loading data..." />
      )}
      
      {/* Save button */}
      <Button 
        onClick={() => saveLoader.withLoading(saveData)}
        disabled={saveLoader.isLoading}
      >
        {saveLoader.isLoading ? (
          <><Loader size="sm" className="mr-2" />Saving...</>
        ) : (
          'Save'
        )}
      </Button>
      
      {/* Delete button */}
      <Button 
        onClick={() => deleteLoader.withLoading(deleteData)}
        disabled={deleteLoader.isLoading}
        variant="destructive"
      >
        {deleteLoader.isLoading ? (
          <><Loader size="sm" className="mr-2" />Deleting...</>
        ) : (
          'Delete'
        )}
      </Button>
    </div>
  )
}
```

---

## 📱 **Responsive Usage**

```tsx
<Loader 
  size="sm" // Small on mobile
  className="md:w-6 md:h-6 lg:w-8 lg:h-8" // Responsive sizing
  text="Loading..."
/>
```

---

## 🎨 **Custom Styling**

```tsx
<Loader 
  className="text-blue-500 border-blue-500" // Custom colors
  size="lg"
  text="Custom styled loader"
/>
```

---

## ✅ **Best Practices**

1. **Use appropriate variants**: Spinner for quick actions, skeleton loaders for content
2. **Provide meaningful text**: Help users understand what's happening
3. **Use overlay for forms**: Prevent interaction during submission
4. **Use fullScreen sparingly**: Only for critical operations
5. **Disable buttons**: Prevent multiple submissions during loading
6. **Handle errors**: Always stop loading on error

---

## 🚀 **Quick Start Checklist**

- [ ] Import the loader component
- [ ] Add loading state to your component
- [ ] Use the loader conditionally
- [ ] Add appropriate text/size/variant
- [ ] Test loading and error states

**That's it! Your loader is ready to use! 🎉**
