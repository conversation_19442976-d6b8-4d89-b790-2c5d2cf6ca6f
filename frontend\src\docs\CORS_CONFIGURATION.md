# CORS Configuration Documentation

This document explains the CORS (Cross-Origin Resource Sharing) configuration implemented to fix communication issues between the frontend and backend of the Kirana Shop application.

## Problem Description

The application was experiencing CORS errors when the frontend (running on Vite dev server) tried to communicate with the backend (Express server). This is a common issue in development environments where:

- **Frontend**: Runs on `http://localhost:5173` or `http://localhost:5174` (Vite default ports)
- **Backend**: Runs on `http://localhost:5000` (Express server)

Since these are different origins (different ports), browsers block requests due to the Same-Origin Policy unless CORS is properly configured.

## Solution Implemented

### 1. Backend CORS Configuration (`backend/src/index.ts`)

**What was changed:**
- Replaced basic `app.use(cors())` with detailed CORS configuration
- Added specific allowed origins for development
- Configured allowed methods and headers
- Enabled credentials support

**Configuration details:**
```typescript
const corsOptions = {
    origin: [
        'http://localhost:5173', // Vite default port
        'http://localhost:5174', // Vite alternative port
        'http://localhost:3000', // React default port (if needed)
        'http://127.0.0.1:5173',
        'http://127.0.0.1:5174',
        'http://127.0.0.1:3000'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type', 
        'Authorization', 
        'X-Requested-With',
        'Accept',
        'Origin'
    ],
    credentials: true, // Allow cookies and credentials
    optionsSuccessStatus: 200 // For legacy browser support
};
```

**Why this works:**
- Explicitly allows requests from frontend development servers
- Supports all necessary HTTP methods for CRUD operations
- Includes all common headers that might be sent by the frontend
- Enables credential sharing for future authentication features

### 2. Frontend Proxy Configuration (`frontend/vite.config.ts`)

**What was added:**
```typescript
server: {
    proxy: {
        '/api': {
            target: 'http://localhost:5000',
            changeOrigin: true,
            secure: false,
            rewrite: (path) => path.replace(/^\/api/, '/api')
        }
    }
}
```

**How this works:**
- When frontend makes a request to `/api/*`, Vite proxy forwards it to `http://localhost:5000/api/*`
- `changeOrigin: true` changes the origin header to match the target
- `secure: false` allows HTTP connections (for development)
- This eliminates CORS issues by making requests appear to come from the same origin

### 3. Centralized API Configuration (`frontend/src/lib/api.ts`)

**What was created:**
- Centralized API configuration file
- Reusable API request functions
- Error handling utilities
- Type-safe API calls

**Key features:**
```typescript
// Base configuration
const API_CONFIG = {
    baseURL: '/api',
    defaultHeaders: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    timeout: 10000,
};

// Enhanced fetch wrapper
export const apiRequest = async <T = any>(
    endpoint: string,
    options: RequestInit = {}
): Promise<T> => {
    // Implementation with error handling and timeout
};

// Product-specific API functions
export const productAPI = {
    getAll: () => apiRequest('/products'),
    getById: (id) => apiRequest(`/products/${id}`),
    create: (data) => apiRequest('/products', { method: 'POST', body: JSON.stringify(data) }),
    // ... more methods
};
```

### 4. Updated Frontend API Calls

**What was changed:**
- Replaced direct `fetch()` calls with centralized API functions
- Added proper error handling
- Improved type safety
- Better user feedback

**Example in AddProduct component:**
```typescript
// Before
fetch('/api/products', {
    method: "GET",
    headers: { "Content-Type": "application/json" },
}).then(res => res.json()).then(data => {
    // Handle response
});

// After
const response = await productAPI.create(productData);
console.log("Product added:", response);
alert.showSuccess(`Product "${formData.name}" added successfully!`);
```

## Testing the Configuration

### 1. Start Both Servers
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 2. Test API Endpoints

**Health Check:**
- Open browser to `http://localhost:5173`
- Open browser console
- Run: `fetch('/api/health').then(r => r.json()).then(console.log)`
- Should return server health status

**Product API:**
- Navigate to Add Product page
- Fill out the form and submit
- Check browser console for successful API call
- No CORS errors should appear

### 3. Verify CORS Headers

In browser developer tools (Network tab):
- Make an API request
- Check response headers for:
  - `Access-Control-Allow-Origin: http://localhost:5173`
  - `Access-Control-Allow-Methods: GET, POST, PUT, DELETE, PATCH, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, ...`

## Production Considerations

### 1. Environment-Specific Configuration

For production, update CORS origins:
```typescript
const corsOptions = {
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com', 'https://www.yourdomain.com']
        : ['http://localhost:5173', 'http://localhost:5174'],
    // ... other options
};
```

### 2. Remove Proxy Configuration

In production, the frontend will be served from the same domain as the backend, so the Vite proxy configuration won't be needed.

### 3. Security Headers

Consider adding additional security headers:
```typescript
app.use(helmet()); // Security headers
app.use(compression()); // Response compression
```

## Troubleshooting

### Common Issues:

1. **Still getting CORS errors:**
   - Check if both servers are running
   - Verify frontend port matches CORS configuration
   - Clear browser cache

2. **Proxy not working:**
   - Restart Vite dev server after config changes
   - Check Vite console for proxy logs

3. **API calls failing:**
   - Check backend server logs
   - Verify API endpoints exist
   - Test endpoints directly with tools like Postman

### Debug Commands:

```bash
# Check if backend is running
curl http://localhost:5000/health

# Check if proxy is working
curl http://localhost:5173/api/health
```

## Benefits of This Implementation

1. **Development Experience**: No more CORS errors during development
2. **Maintainability**: Centralized API configuration
3. **Error Handling**: Better error messages and user feedback
4. **Type Safety**: TypeScript support for API calls
5. **Scalability**: Easy to add new API endpoints
6. **Security**: Proper CORS configuration for production

This configuration provides a robust foundation for frontend-backend communication while maintaining security best practices.
