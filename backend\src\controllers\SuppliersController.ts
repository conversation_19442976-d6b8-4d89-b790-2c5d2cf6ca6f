import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { Suppliers } from "../models/Suppliers.model";
import { SupplierService } from "../services/SupplierService";

export class SuppliersController {
    private supplierService: SupplierService;

    constructor() {
        this.supplierService = new SupplierService(AppDataSource);
    }

    async allSuppliers(req: Request, res: Response): Promise<void> {
        try {

            const suppliers = await this.supplierService.getAllSuppliers();
            if (!suppliers) {
                res.status(200).json({ success: false, message: "No suppliers found" });
                return;
            }

            res.status(200).json({ success: true, data: suppliers, message: "Suppliers fetched successfully" });
        } catch (error) {
            console.error("Error fetching suppliers:", error);
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch suppliers",
                error: error
            });
            return;
        }
    }

    async getSupplier(req: Request, res: Response): Promise<void> {
        try {
            const id = Number(req.params.id);
            const supplier = await this.supplierService.getSupplierById(id);
            if (!supplier) {
                res.status(200).json({
                    success: false,
                    message: "Supplier not found"
                });
                return;
            }
            res.status(200).json({
                success: true,
                data: supplier
            });
            return;
        } catch (error) {
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to fetch supplier",
                error: error
            });
        }
    }

    async addSupplier(req: Request, res: Response): Promise<void> {
        try {
            const newSupplier = await this.supplierService.addSupplier(req.body);
            res.status(200).json({
                success: true,
                data: newSupplier,
                message: "Supplier added successfully"
            });
            return;
        } catch (error) {
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to add supplier",
                error: error
            });
            return;
        }
    }

    async editSupplier(req: Request, res: Response): Promise<void> {
        try {
            const id = Number(req.params.id);
            const updatedSupplier = await this.supplierService.updateSupplier(id, req.body);
            res.status(200).json({
                success: true,
                data: updatedSupplier,
                message: "Supplier updated successfully"
            });
            return;
        } catch (error) {
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to edit supplier",
                error: error
            });
            return;
        }
    }

    async deleteSupplier(req: Request, res: Response): Promise<void> {
        try {
            const id = Number(req.params.id);
            await this.supplierService.deleteSupplier(id);
            res.status(200).json({
                success: true,
                message: "Supplier deleted successfully"
            });
            return;
        } catch (error) {
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to delete supplier",
                error: error
            });
            return;
        }
    }

    async toggleSupplierStatus(req: Request, res: Response): Promise<void> {
        try {
            const id = Number(req.params.id);
            const updatedSupplier = await this.supplierService.supplierStatus(id);
            res.status(200).json({
                success: true,
                data: updatedSupplier,
                message: "Supplier status updated successfully"
            });
            return;
        } catch (error) {
            res.status(200).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to update supplier status",
                error: error
            });
            return;
        }
    }
}
