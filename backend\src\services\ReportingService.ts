import { Repository, DataSource, Between } from "typeorm";
import { Sales } from "../models/Sales.model";
import { Customer } from "../models/Customer.model";
import { Product } from "../models/Product.model";
import { DailySalesSummary } from "../models/DailySalesSummary.model";
import { SaleItem } from "../models/SaleItem.model";
import { CreditTransaction } from "../models/CreditTransaction.model";

export interface DateRangeFilter {
    startDate: Date;
    endDate: Date;
}

export interface SalesAnalyticsReport {
    totalSales: number;
    totalTransactions: number;
    averageOrderValue: number;
    totalItemsSold: number;
    totalGstCollected: number;
    paymentMethodBreakdown: Array<{
        method: string;
        amount: number;
        percentage: number;
        transactionCount: number;
    }>;
    dailyTrends: Array<{
        date: string;
        sales: number;
        transactions: number;
        averageOrderValue: number;
    }>;
    topProducts: Array<{
        productId: number;
        productName: string;
        quantitySold: number;
        totalRevenue: number;
        averagePrice: number;
    }>;
    customerAnalysis: {
        totalCustomers: number;
        newCustomers: number;
        returningCustomers: number;
        topCustomers: Array<{
            customerId: number;
            customerName: string;
            totalPurchases: number;
            transactionCount: number;
        }>;
    };
}

export interface CustomerAnalyticsReport {
    totalCustomers: number;
    activeCustomers: number;
    categorySummary: Record<string, number>;
    creditAnalysis: {
        totalCreditOutstanding: number;
        overdueAmount: number;
        overdueCustomers: number;
        averageCreditLimit: number;
        creditUtilizationRate: number;
    };
    loyaltyAnalysis: {
        averageLoyaltyPoints: number;
        topLoyaltyCustomers: Array<{
            customerId: number;
            customerName: string;
            loyaltyPoints: number;
            totalPurchases: number;
        }>;
    };
    purchasePatterns: Array<{
        category: string;
        customerCount: number;
        averagePurchaseValue: number;
        totalRevenue: number;
    }>;
}

export interface ProductPerformanceReport {
    totalProducts: number;
    lowStockProducts: number;
    outOfStockProducts: number;
    topSellingProducts: Array<{
        productId: number;
        productName: string;
        category: string;
        quantitySold: number;
        revenue: number;
        profitMargin: number;
    }>;
    categoryPerformance: Array<{
        categoryName: string;
        productCount: number;
        totalRevenue: number;
        averagePrice: number;
        quantitySold: number;
    }>;
    slowMovingProducts: Array<{
        productId: number;
        productName: string;
        currentStock: number;
        lastSaleDate: Date | null;
        daysSinceLastSale: number;
    }>;
}

export class ReportingService {
    private salesRepository: Repository<Sales>;
    private customerRepository: Repository<Customer>;
    private productRepository: Repository<Product>;
    private dailySalesSummaryRepository: Repository<DailySalesSummary>;
    private saleItemRepository: Repository<SaleItem>;
    private creditTransactionRepository: Repository<CreditTransaction>;
    private dataSource: DataSource;

    constructor(dataSource: DataSource) {
        this.dataSource = dataSource;
        this.salesRepository = dataSource.getRepository(Sales);
        this.customerRepository = dataSource.getRepository(Customer);
        this.productRepository = dataSource.getRepository(Product);
        this.dailySalesSummaryRepository = dataSource.getRepository(DailySalesSummary);
        this.saleItemRepository = dataSource.getRepository(SaleItem);
        this.creditTransactionRepository = dataSource.getRepository(CreditTransaction);
    }

    async getSalesAnalytics(dateRange: DateRangeFilter): Promise<SalesAnalyticsReport> {
        const { startDate, endDate } = dateRange;

        // Get sales data for the period
        const sales = await this.salesRepository.find({
            where: {
                saleDate: Between(startDate, endDate),
                status: "Completed"
            },
            relations: ["customer", "items", "items.product"]
        });

        // Calculate basic metrics
        const totalSales = sales.reduce((sum, sale) => sum + sale.grandTotal, 0);
        const totalTransactions = sales.length;
        const averageOrderValue = totalTransactions > 0 ? totalSales / totalTransactions : 0;
        const totalItemsSold = sales.reduce((sum, sale) => sum + sale.totalItems, 0);
        const totalGstCollected = sales.reduce((sum, sale) => sum + sale.totalGst, 0);

        // Payment method breakdown
        const paymentBreakdown = this.calculatePaymentMethodBreakdown(sales);

        // Daily trends
        const dailyTrends = await this.calculateDailyTrends(startDate, endDate);

        // Top products
        const topProducts = await this.getTopProducts(startDate, endDate);

        // Customer analysis
        const customerAnalysis = await this.getCustomerAnalysis(sales, startDate, endDate);

        return {
            totalSales,
            totalTransactions,
            averageOrderValue,
            totalItemsSold,
            totalGstCollected,
            paymentMethodBreakdown: paymentBreakdown,
            dailyTrends,
            topProducts,
            customerAnalysis
        };
    }

    async getCustomerAnalytics(): Promise<CustomerAnalyticsReport> {
        const totalCustomers = await this.customerRepository.count();
        const activeCustomers = await this.customerRepository.count({ where: { isActive: true } });

        // Category summary
        const categoryQuery = await this.customerRepository
            .createQueryBuilder("customer")
            .select("customer.category", "category")
            .addSelect("COUNT(*)", "count")
            .where("customer.isActive = true")
            .groupBy("customer.category")
            .getRawMany();

        const categorySummary = categoryQuery.reduce((acc, item) => {
            acc[item.category] = parseInt(item.count);
            return acc;
        }, {} as Record<string, number>);

        // Credit analysis
        const creditAnalysis = await this.getCreditAnalysis();

        // Loyalty analysis
        const loyaltyAnalysis = await this.getLoyaltyAnalysis();

        // Purchase patterns
        const purchasePatterns = await this.getPurchasePatterns();

        return {
            totalCustomers,
            activeCustomers,
            categorySummary,
            creditAnalysis,
            loyaltyAnalysis,
            purchasePatterns
        };
    }

    async getProductPerformance(dateRange: DateRangeFilter): Promise<ProductPerformanceReport> {
        const totalProducts = await this.productRepository.count();
        const lowStockProducts = await this.productRepository.count({
            where: { status: "low" }
        });
        const outOfStockProducts = await this.productRepository.count({
            where: { status: "out" }
        });

        // Top selling products
        const topSellingProducts = await this.getTopSellingProducts(dateRange);

        // Category performance
        const categoryPerformance = await this.getCategoryPerformance(dateRange);

        // Slow moving products
        const slowMovingProducts = await this.getSlowMovingProducts();

        return {
            totalProducts,
            lowStockProducts,
            outOfStockProducts,
            topSellingProducts,
            categoryPerformance,
            slowMovingProducts
        };
    }

    private calculatePaymentMethodBreakdown(sales: Sales[]) {
        const breakdown = new Map<string, { amount: number; count: number }>();

        sales.forEach(sale => {
            const existing = breakdown.get(sale.paymentMethod) || { amount: 0, count: 0 };
            breakdown.set(sale.paymentMethod, {
                amount: existing.amount + sale.grandTotal,
                count: existing.count + 1
            });
        });

        const totalSales = sales.reduce((sum, sale) => sum + sale.grandTotal, 0);

        return Array.from(breakdown.entries()).map(([method, data]) => ({
            method,
            amount: data.amount,
            percentage: totalSales > 0 ? (data.amount / totalSales) * 100 : 0,
            transactionCount: data.count
        }));
    }

    private async calculateDailyTrends(startDate: Date, endDate: Date) {
        const dailySummaries = await this.dailySalesSummaryRepository.find({
            where: {
                summaryDate: Between(startDate, endDate)
            },
            order: { summaryDate: "ASC" }
        });

        return dailySummaries.map(summary => ({
            date: summary.summaryDate.toISOString().split('T')[0],
            sales: summary.totalSales,
            transactions: summary.totalTransactions,
            averageOrderValue: summary.averageOrderValue
        }));
    }

    private async getTopProducts(startDate: Date, endDate: Date, limit: number = 10) {
        const topProducts = await this.saleItemRepository
            .createQueryBuilder("saleItem")
            .leftJoin("saleItem.sale", "sale")
            .leftJoin("saleItem.product", "product")
            .select("product.id", "productId")
            .addSelect("product.name", "productName")
            .addSelect("SUM(saleItem.quantity)", "quantitySold")
            .addSelect("SUM(saleItem.finalAmount)", "totalRevenue")
            .addSelect("AVG(saleItem.unitPrice)", "averagePrice")
            .where("sale.saleDate BETWEEN :startDate AND :endDate", { startDate, endDate })
            .andWhere("sale.status = 'Completed'")
            .groupBy("product.id")
            .orderBy("quantitySold", "DESC")
            .limit(limit)
            .getRawMany();

        return topProducts.map(item => ({
            productId: item.productId,
            productName: item.productName,
            quantitySold: parseFloat(item.quantitySold),
            totalRevenue: parseFloat(item.totalRevenue),
            averagePrice: parseFloat(item.averagePrice)
        }));
    }

    private async getCustomerAnalysis(sales: Sales[], startDate: Date, endDate: Date) {
        const customerIds = [...new Set(sales.filter(s => s.customer).map(s => s.customer!.id))];
        const totalCustomers = customerIds.length;

        // Get new customers in this period
        const newCustomers = await this.customerRepository.count({
            where: {
                createdAt: Between(startDate, endDate)
            }
        });

        const returningCustomers = totalCustomers - newCustomers;

        // Top customers by purchase value
        const customerSales = new Map<number, { name: string; total: number; count: number }>();
        
        sales.forEach(sale => {
            if (sale.customer) {
                const existing = customerSales.get(sale.customer.id) || { 
                    name: sale.customer.name, 
                    total: 0, 
                    count: 0 
                };
                customerSales.set(sale.customer.id, {
                    name: existing.name,
                    total: existing.total + sale.grandTotal,
                    count: existing.count + 1
                });
            }
        });

        const topCustomers = Array.from(customerSales.entries())
            .map(([id, data]) => ({
                customerId: id,
                customerName: data.name,
                totalPurchases: data.total,
                transactionCount: data.count
            }))
            .sort((a, b) => b.totalPurchases - a.totalPurchases)
            .slice(0, 10);

        return {
            totalCustomers,
            newCustomers,
            returningCustomers,
            topCustomers
        };
    }

    private async getCreditAnalysis() {
        const creditStats = await this.customerRepository
            .createQueryBuilder("customer")
            .select("SUM(customer.currentCredit)", "totalCredit")
            .addSelect("AVG(customer.creditLimit)", "averageLimit")
            .addSelect("COUNT(CASE WHEN customer.currentCredit > 0 THEN 1 END)", "customersWithCredit")
            .where("customer.isActive = true")
            .getRawOne();

        // Get overdue amount
        const overdueStats = await this.creditTransactionRepository
            .createQueryBuilder("credit")
            .select("SUM(credit.amount)", "overdueAmount")
            .addSelect("COUNT(DISTINCT credit.customer)", "overdueCustomers")
            .where("credit.dueDate < :today", { today: new Date() })
            .andWhere("credit.status = 'Active'")
            .getRawOne();

        const totalCreditOutstanding = parseFloat(creditStats.totalCredit) || 0;
        const averageCreditLimit = parseFloat(creditStats.averageLimit) || 0;
        const creditUtilizationRate = averageCreditLimit > 0 
            ? (totalCreditOutstanding / averageCreditLimit) * 100 
            : 0;

        return {
            totalCreditOutstanding,
            overdueAmount: parseFloat(overdueStats.overdueAmount) || 0,
            overdueCustomers: parseInt(overdueStats.overdueCustomers) || 0,
            averageCreditLimit,
            creditUtilizationRate
        };
    }

    private async getLoyaltyAnalysis() {
        const loyaltyStats = await this.customerRepository
            .createQueryBuilder("customer")
            .select("AVG(customer.loyaltyPoints)", "averagePoints")
            .where("customer.isActive = true")
            .getRawOne();

        const topLoyaltyCustomers = await this.customerRepository.find({
            where: { isActive: true },
            order: { loyaltyPoints: "DESC" },
            take: 10
        });

        return {
            averageLoyaltyPoints: parseFloat(loyaltyStats.averagePoints) || 0,
            topLoyaltyCustomers: topLoyaltyCustomers.map(customer => ({
                customerId: customer.id,
                customerName: customer.name,
                loyaltyPoints: customer.loyaltyPoints,
                totalPurchases: customer.totalPurchases
            }))
        };
    }

    private async getPurchasePatterns() {
        const patterns = await this.customerRepository
            .createQueryBuilder("customer")
            .select("customer.category", "category")
            .addSelect("COUNT(*)", "customerCount")
            .addSelect("AVG(customer.totalPurchases)", "averagePurchaseValue")
            .addSelect("SUM(customer.totalPurchases)", "totalRevenue")
            .where("customer.isActive = true")
            .groupBy("customer.category")
            .getRawMany();

        return patterns.map(pattern => ({
            category: pattern.category,
            customerCount: parseInt(pattern.customerCount),
            averagePurchaseValue: parseFloat(pattern.averagePurchaseValue) || 0,
            totalRevenue: parseFloat(pattern.totalRevenue) || 0
        }));
    }

    private async getTopSellingProducts(dateRange: DateRangeFilter, limit: number = 10) {
        const { startDate, endDate } = dateRange;

        const topProducts = await this.saleItemRepository
            .createQueryBuilder("saleItem")
            .leftJoin("saleItem.sale", "sale")
            .leftJoin("saleItem.product", "product")
            .leftJoin("product.category", "category")
            .select("product.id", "productId")
            .addSelect("product.name", "productName")
            .addSelect("category.name", "category")
            .addSelect("SUM(saleItem.quantity)", "quantitySold")
            .addSelect("SUM(saleItem.finalAmount)", "revenue")
            .addSelect("AVG((saleItem.unitPrice - product.purchasePrice) / saleItem.unitPrice * 100)", "profitMargin")
            .where("sale.saleDate BETWEEN :startDate AND :endDate", { startDate, endDate })
            .andWhere("sale.status = 'Completed'")
            .groupBy("product.id")
            .orderBy("quantitySold", "DESC")
            .limit(limit)
            .getRawMany();

        return topProducts.map(item => ({
            productId: item.productId,
            productName: item.productName,
            category: item.category,
            quantitySold: parseFloat(item.quantitySold),
            revenue: parseFloat(item.revenue),
            profitMargin: parseFloat(item.profitMargin) || 0
        }));
    }

    private async getCategoryPerformance(dateRange: DateRangeFilter) {
        const { startDate, endDate } = dateRange;

        const categoryPerformance = await this.saleItemRepository
            .createQueryBuilder("saleItem")
            .leftJoin("saleItem.sale", "sale")
            .leftJoin("saleItem.product", "product")
            .leftJoin("product.category", "category")
            .select("category.name", "categoryName")
            .addSelect("COUNT(DISTINCT product.id)", "productCount")
            .addSelect("SUM(saleItem.finalAmount)", "totalRevenue")
            .addSelect("AVG(saleItem.unitPrice)", "averagePrice")
            .addSelect("SUM(saleItem.quantity)", "quantitySold")
            .where("sale.saleDate BETWEEN :startDate AND :endDate", { startDate, endDate })
            .andWhere("sale.status = 'Completed'")
            .groupBy("category.id")
            .orderBy("totalRevenue", "DESC")
            .getRawMany();

        return categoryPerformance.map(item => ({
            categoryName: item.categoryName,
            productCount: parseInt(item.productCount),
            totalRevenue: parseFloat(item.totalRevenue),
            averagePrice: parseFloat(item.averagePrice),
            quantitySold: parseFloat(item.quantitySold)
        }));
    }

    private async getSlowMovingProducts(limit: number = 20) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const slowMovingProducts = await this.productRepository
            .createQueryBuilder("product")
            .leftJoin("product.saleItems", "saleItem")
            .leftJoin("saleItem.sale", "sale")
            .select("product.id", "productId")
            .addSelect("product.name", "productName")
            .addSelect("product.stock", "currentStock")
            .addSelect("MAX(sale.saleDate)", "lastSaleDate")
            .where("product.stock > 0")
            .groupBy("product.id")
            .having("MAX(sale.saleDate) < :thirtyDaysAgo OR MAX(sale.saleDate) IS NULL", { thirtyDaysAgo })
            .orderBy("MAX(sale.saleDate)", "ASC")
            .limit(limit)
            .getRawMany();

        return slowMovingProducts.map(item => {
            const lastSaleDate = item.lastSaleDate ? new Date(item.lastSaleDate) : null;
            const daysSinceLastSale = lastSaleDate 
                ? Math.floor((new Date().getTime() - lastSaleDate.getTime()) / (1000 * 60 * 60 * 24))
                : 999;

            return {
                productId: item.productId,
                productName: item.productName,
                currentStock: item.currentStock,
                lastSaleDate,
                daysSinceLastSale
            };
        });
    }
}
