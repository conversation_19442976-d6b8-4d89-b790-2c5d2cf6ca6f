// API configuration and base utilities
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';

// API Response types
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    message?: string;
    pagination?: {
        page: number;
        limit: number;
        total: number;
    };
}

export interface ApiError {
    success: false;
    message: string;
    error?: any;
}

// Base API client class
class ApiClient {
    private baseURL: string;

    constructor(baseURL: string) {
        this.baseURL = baseURL;
    }

    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        const url = `${this.baseURL}${endpoint}`;

        const config: RequestInit = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            throw {
                success: false,
                message: error instanceof Error ? error.message : 'An unknown error occurred',
                error
            } as ApiError;
        }
    }

    async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
        
        const url = new URL(`${this.baseURL}${endpoint}`);
            
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    url.searchParams.append(key, String(value));
                }
            });
        }
        const path = url.pathname.replace('/api', '') + url.search;
        return this.request<T>(path);
    }

    async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined,
        });
    }

    async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
        return this.request<T>(endpoint, {
            method: 'DELETE',
        });
    }
}

// Create and export the API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Utility function to handle API errors
export const handleApiError = (error: any): string => {
    if (error && typeof error === 'object' && 'message' in error) {
        return error.message;
    }
    return 'An unexpected error occurred';
};

// Utility function to format API responses for UI consumption
export const formatApiResponse = <T>(response: ApiResponse<T>) => {
    return {
        data: response.data,
        success: response.success,
        message: response.message,
        pagination: response.pagination
    };
};

// Loading state management
export interface LoadingState {
    isLoading: boolean;
    error: string | null;
}

export const createLoadingState = (): LoadingState => ({
    isLoading: false,
    error: null
});

export const setLoading = (state: LoadingState, loading: boolean): LoadingState => ({
    ...state,
    isLoading: loading,
    error: loading ? null : state.error
});

export const setError = (state: LoadingState, error: string): LoadingState => ({
    ...state,
    isLoading: false,
    error
});
