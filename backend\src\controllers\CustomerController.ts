import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig"; 
import { CustomerService, CreateCustomerRequest, UpdateCustomerRequest, CreditPaymentRequest } from "../services/CustomerService";

export class CustomerController {
    private customerService: CustomerService;

    constructor() {
        this.customerService = new CustomerService(AppDataSource);
    }

    // Create a new customer
    createCustomer = async (req: Request, res: Response): Promise<void> => {
        try {
            const customerData: CreateCustomerRequest = req.body;

            // Validate required fields
            if (!customerData.name || !customerData.phone) {
                res.status(400).json({
                    success: false,
                    message: "Name and phone are required"
                });
                return;
            }

            // Validate phone number format (basic validation)
            const phoneRegex = /^[6-9]\d{9}$/;
            if (!phoneRegex.test(customerData.phone)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid phone number format"
                });
                return;
            }

            const customer = await this.customerService.createCustomer(customerData);

            res.status(201).json({
                success: true,
                message: "Customer created successfully",
                data: customer
            });

        } catch (error) {
            console.error("Error creating customer:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to create customer"
            });
        }
    };

    // Update customer
    updateCustomer = async (req: Request, res: Response): Promise<void> => {
        try {
            const id = parseInt(req.params.id);
            const updateData: UpdateCustomerRequest = req.body;

            if (isNaN(id)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid customer ID"
                });
                return;
            }

            // Validate phone number format if provided
            if (updateData.phone) {
                const phoneRegex = /^[6-9]\d{9}$/;
                if (!phoneRegex.test(updateData.phone)) {
                    res.status(400).json({
                        success: false,
                        message: "Invalid phone number format"
                    });
                    return;
                }
            }

            const customer = await this.customerService.updateCustomer(id, updateData);

            res.json({
                success: true,
                message: "Customer updated successfully",
                data: customer
            });

        } catch (error) {
            console.error("Error updating customer:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to update customer"
            });
        }
    };

    // Get customer by ID
    getCustomerById = async (req: Request, res: Response): Promise<void> => {
        try {
            const id = parseInt(req.params.id);

            if (isNaN(id)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid customer ID"
                });
                return;
            }

            const customer = await this.customerService.getCustomerById(id);

            if (!customer) {
                res.status(404).json({
                    success: false,
                    message: "Customer not found"
                });
                return;
            }

            res.json({
                success: true,
                data: customer
            });

        } catch (error) {
            console.error("Error fetching customer:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch customer"
            });
        }
    };

    // Search customers with filters
    searchCustomers = async (req: Request, res: Response): Promise<void> => {
        try {
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            const filters: any = {};

            if (req.query.name) {
                filters.name = req.query.name as string;
            }

            if (req.query.phone) {
                filters.phone = req.query.phone as string;
            }

            if (req.query.category) {
                filters.category = req.query.category as "Regular" | "Wholesale" | "VIP";
            }

            if (req.query.creditStatus) {
                filters.creditStatus = req.query.creditStatus as "Good" | "Warning" | "Overdue";
            }

            if (req.query.isActive !== undefined) {
                filters.isActive = req.query.isActive === "true";
            }

            const result = await this.customerService.searchCustomers(filters, page, limit);

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error("Error searching customers:", error);
            res.status(500).json({
                success: false,
                message: "Failed to search customers"
            });
        }
    };

    // Record credit payment
    recordCreditPayment = async (req: Request, res: Response): Promise<void> => {
        try {
            const paymentData: CreditPaymentRequest = req.body;

            // Validate required fields
            if (!paymentData.customerId || !paymentData.amount || !paymentData.paymentMethod) {
                res.status(400).json({
                    success: false,
                    message: "Customer ID, amount, and payment method are required"
                });
                return;
            }

            if (paymentData.amount <= 0) {
                res.status(400).json({
                    success: false,
                    message: "Payment amount must be greater than zero"
                });
                return;
            }

            const transaction = await this.customerService.recordCreditPayment(paymentData);

            res.json({
                success: true,
                message: "Credit payment recorded successfully",
                data: transaction
            });

        } catch (error) {
            console.error("Error recording credit payment:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to record credit payment"
            });
        }
    };

    // Get customer credit history
    getCustomerCreditHistory = async (req: Request, res: Response): Promise<void> => {
        try {
            const customerId = parseInt(req.params.customerId);
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            if (isNaN(customerId)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid customer ID"
                });
                return;
            }

            const result = await this.customerService.getCustomerCreditHistory(customerId, page, limit);

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error("Error fetching customer credit history:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch customer credit history"
            });
        }
    };

    // Get customer sales history
    getCustomerSalesHistory = async (req: Request, res: Response): Promise<void> => {
        try {
            const customerId = parseInt(req.params.customerId);
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;

            if (isNaN(customerId)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid customer ID"
                });
                return;
            }

            const result = await this.customerService.getCustomerSalesHistory(customerId, page, limit);

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error("Error fetching customer sales history:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch customer sales history"
            });
        }
    };

    // Get overdue customers
    getOverdueCustomers = async (_req: Request, res: Response): Promise<void> => {
        try {
            const customers = await this.customerService.getOverdueCustomers();

            res.json({
                success: true,
                data: customers
            });

        } catch (error) {
            console.error("Error fetching overdue customers:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch overdue customers"
            });
        }
    };

    // Delete customer (soft delete)
    deleteCustomer = async (req: Request, res: Response): Promise<void> => {
        try {
            const id = parseInt(req.params.id);

            if (isNaN(id)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid customer ID"
                });
                return;
            }

            await this.customerService.deleteCustomer(id);

            res.json({
                success: true,
                message: "Customer deleted successfully"
            });

        } catch (error) {
            console.error("Error deleting customer:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to delete customer"
            });
        }
    };

    // Get customer statistics
    getCustomerStatistics = async (_req: Request, res: Response): Promise<void> => {
        try {
            const statistics = await this.customerService.getCustomerStatistics();

            res.json({
                success: true,
                data: statistics
            });

        } catch (error) {
            console.error("Error fetching customer statistics:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch customer statistics"
            });
        }
    };

    // Quick search for customer selection
    quickSearchCustomers = async (req: Request, res: Response): Promise<void> => {
        try {
            const { query } = req.query;

            if (!query || typeof query !== "string") {
                res.status(400).json({
                    success: false,
                    message: "Search query is required"
                });
                return;
            }

            // Search by phone first, then by name
            let customers = await this.customerService.getCustomersByPhone(query);
            
            if (customers.length === 0) {
                customers = await this.customerService.getCustomersByName(query);
            }

            res.json({
                success: true,
                data: customers
            });

        } catch (error) {
            console.error("Error in quick search:", error);
            res.status(500).json({
                success: false,
                message: "Failed to search customers"
            });
        }
    };
}
