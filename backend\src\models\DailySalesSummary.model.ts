import {
    En<PERSON>ty,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    Index,
} from "typeorm";

@Entity("daily_sales_summary")
export class DailySalesSummary {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "date", unique: true, name: "summary_date" })
    @Index("idx_summary_date")
    summaryDate!: Date;

    @Column("decimal", { precision: 12, scale: 2, default: 0.00, name: "total_sales" })
    totalSales!: number;

    @Column({ type: "int", default: 0, name: "total_transactions" })
    totalTransactions!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "cash_sales" })
    cashSales!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "upi_sales" })
    upiSales!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "credit_sales" })
    creditSales!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "card_sales" })
    cardSales!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "total_gst_collected" })
    totalGstCollected!: number;

    @Column({ type: "int", default: 0, name: "total_items_sold" })
    totalItemsSold!: number;

    @Column({ type: "int", default: 0, name: "unique_customers" })
    uniqueCustomers!: number;

    @Column({ type: "int", default: 0, name: "new_customers" })
    newCustomers!: number;

    @Column("decimal", { precision: 10, scale: 2, default: 0.00, name: "average_order_value" })
    averageOrderValue!: number;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    @UpdateDateColumn({
        name: "updated_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    updatedAt!: Date;

    // Virtual properties for calculations
    get cashPercentage(): number {
        return this.totalSales > 0 ? (this.cashSales / this.totalSales) * 100 : 0;
    }

    get upiPercentage(): number {
        return this.totalSales > 0 ? (this.upiSales / this.totalSales) * 100 : 0;
    }

    get creditPercentage(): number {
        return this.totalSales > 0 ? (this.creditSales / this.totalSales) * 100 : 0;
    }

    get cardPercentage(): number {
        return this.totalSales > 0 ? (this.cardSales / this.totalSales) * 100 : 0;
    }

    get gstPercentage(): number {
        return this.totalSales > 0 ? (this.totalGstCollected / this.totalSales) * 100 : 0;
    }

    get averageItemsPerTransaction(): number {
        return this.totalTransactions > 0 ? this.totalItemsSold / this.totalTransactions : 0;
    }

    get digitalPaymentPercentage(): number {
        const digitalSales = this.upiSales + this.cardSales;
        return this.totalSales > 0 ? (digitalSales / this.totalSales) * 100 : 0;
    }

    get paymentMethodBreakdown(): Array<{ method: string; amount: number; percentage: number }> {
        return [
            { method: "Cash", amount: this.cashSales, percentage: this.cashPercentage },
            { method: "UPI", amount: this.upiSales, percentage: this.upiPercentage },
            { method: "Credit", amount: this.creditSales, percentage: this.creditPercentage },
            { method: "Card", amount: this.cardSales, percentage: this.cardPercentage }
        ].filter(item => item.amount > 0);
    }
}
