import { Link, useLocation } from "react-router-dom"
import { Home, ArrowLeft, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useNavigation } from "@/hooks/useNavigation"

export default function NotFound() {
    const location = useLocation()
    const { validRoutes } = useNavigation()

    return (
        <div className="flex flex-1 flex-col items-center justify-center min-h-[60vh] text-center">
            <div className="space-y-6">
                {/* 404 Error */}
                <div className="space-y-2">
                    <h1 className="text-6xl font-bold text-muted-foreground">404</h1>
                    <h2 className="text-2xl font-semibold">Page Not Found</h2>
                    <p className="text-muted-foreground max-w-md">
                        The page <code className="bg-muted px-2 py-1 rounded text-sm">{location.pathname}</code> doesn't exist or has been moved.
                    </p>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button asChild>
                        <Link to="/">
                            <Home className="mr-2 h-4 w-4" />
                            Go to Dashboard
                        </Link>
                    </Button>
                    <Button variant="outline" onClick={() => window.history.back()}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Go Back
                    </Button>
                </div>

                {/* Available Routes */}
                <div className="pt-8">
                    <p className="text-sm text-muted-foreground mb-4">Available pages in Kirana Shop:</p>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 max-w-md">
                        {validRoutes.filter(route => route !== "/").map((route) => (
                            <Button key={route} variant="ghost" size="sm" asChild>
                                <Link to={route}>
                                    {route.replace("/", "").charAt(0).toUpperCase() + route.slice(2) || "Dashboard"}
                                </Link>
                            </Button>
                        ))}
                    </div>
                </div>

                {/* Search Suggestion */}
                <div className="pt-4 border-t">
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                        <Search className="h-3 w-3" />
                        <span>Looking for something specific? Check the sidebar navigation.</span>
                    </div>
                </div>
            </div>
        </div>
    )
}
