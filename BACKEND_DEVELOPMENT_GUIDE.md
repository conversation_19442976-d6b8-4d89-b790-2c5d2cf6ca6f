# Backend Development Guide - Kirana Shop Management System

## 🏗️ Architecture Approach

### Layered Architecture Pattern
```
Controllers → Services → Models → Database
```

**Benefits:**
- Separation of Concerns: Each layer has specific responsibility
- Maintainability: Easy to modify one layer without affecting others
- Testability: Each layer can be unit tested independently
- Scalability: Easy to add new features following same pattern

### File Organization Structure
```
backend/src/
├── models/           # Database entities (TypeORM)
├── services/         # Business logic layer
├── controllers/      # HTTP request/response handling
├── routes/           # API endpoint definitions
├── dbconfig.ts       # Database configuration
└── index.ts          # Application entry point
```

## 🎯 Key Design Patterns

### 1. Repository Pattern (via TypeORM)
```typescript
export class CustomerService {
    private customerRepository: Repository<Customer>;
    private creditTransactionRepository: Repository<CreditTransaction>;
    private dataSource: DataSource;

    constructor(dataSource: DataSource) {
        this.dataSource = dataSource;
        this.customerRepository = dataSource.getRepository(Customer);
        this.creditTransactionRepository = dataSource.getRepository(CreditTransaction);
    }
}
```

### 2. Service Layer Pattern
- Single Responsibility: Each service handles one domain
- Business Logic Encapsulation: All business rules in services, not controllers
- Transaction Management: Complex operations use database transactions

### 3. Controller Pattern
```typescript
export class CustomerController {
    private customerService: CustomerService;

    constructor() {
        this.customerService = new CustomerService(AppDataSource);
    }

    createCustomer = async (req: Request, res: Response): Promise<void> => {
        // Validation → Service call → Response formatting
    }
}
```

## 📋 Development Best Practices

### 1. Layered Approach
```typescript
// ✅ Good: Keep layers separate
Controller → Service → Repository → Database

// ❌ Avoid: Direct database access from controllers
Controller → Database (skip this)
```

### 2. Consistent Error Handling Pattern
```typescript
// Use this pattern in all controllers:
try {
    const result = await this.service.method(data);
    res.json({ success: true, data: result });
} catch (error) {
    console.error("Error description:", error);
    res.status(500).json({ 
        success: false, 
        message: "User-friendly error message" 
    });
}
```

### 3. Service Layer Best Practices

#### Use Transactions for Complex Operations
```typescript
async createSale(saleData: CreateSaleRequest): Promise<Sales> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.startTransaction();
    
    try {
        // Multiple database operations
        await queryRunner.commitTransaction();
        return sale;
    } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
    } finally {
        await queryRunner.release();
    }
}
```

#### Create Clear Interfaces
```typescript
// Define clear request/response interfaces
export interface CreateCustomerRequest {
    name: string;
    phone: string;
    email?: string;
    category?: "Regular" | "Wholesale" | "VIP";
    creditLimit?: number;
    gstNumber?: string;
}
```

### 4. Database Model Best Practices

#### Use Explicit Entity Configuration
```typescript
// ✅ Explicit entity imports (recommended)
entities: [Customer, Sales, SaleItem, GstBreakdown, ...],

// ❌ Avoid glob patterns in development
entities: ["src/models/*.ts"],
```

#### Add Proper Indexes and Relations
```typescript
@Column({ type: "varchar", length: 15, unique: true })
@Index("idx_customer_phone")
phone!: string;

@OneToMany(() => Sales, (sale) => sale.customer)
sales!: Sales[];
```

### 5. API Design Standards

#### Consistent Response Format
```typescript
// Success response
{ success: true, data: {...} }

// Error response  
{ success: false, message: "Error description" }

// List response with pagination
{ 
    success: true, 
    data: [...], 
    pagination: { page, limit, total } 
}
```

#### RESTful Route Organization
```typescript
// Group related routes
router.use("/customers", customerRoutes);
router.use("/sales", salesRoutes);
router.use("/reports", reportingRoutes);
```

## 🚀 Recommended Development Workflow

### 1. Development Order
1. **Models First** → Define database structure with proper relations
2. **Services** → Implement business logic and data operations
3. **Controllers** → Handle HTTP requests and responses
4. **Routes** → Wire controllers to endpoints
5. **Testing** → Write and run comprehensive tests

### 2. Code Organization Tips
- **One entity per service** (CustomerService, SalesService, ReportingService)
- **Keep controllers thin** - only handle HTTP concerns
- **Use TypeScript interfaces** for all data contracts
- **Add proper error handling** at every layer
- **Implement validation** at controller level before service calls

### 3. Testing Strategy
```typescript
// Test each layer independently
- Unit tests for services (business logic)
- Integration tests for controllers (API endpoints)
- Database tests for complex queries
- End-to-end tests for complete workflows
```

### 4. Performance Considerations
- **Use database indexes** for frequently queried fields
- **Implement pagination** for list endpoints (customers, sales, etc.)
- **Use transactions** for multi-table operations
- **Add query optimization** for reporting endpoints
- **Cache frequently accessed data** when appropriate

## 💡 Key Implementation Examples

### Transaction Management Example
```typescript
// For operations affecting multiple tables
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.startTransaction();

try {
    // 1. Create sale record
    // 2. Create sale items
    // 3. Update stock levels
    // 4. Create GST breakdown
    // 5. Update customer totals
    
    await queryRunner.commitTransaction();
} catch (error) {
    await queryRunner.rollbackTransaction();
    throw error;
} finally {
    await queryRunner.release();
}
```

### Service Method Structure
```typescript
async createCustomer(customerData: CreateCustomerRequest): Promise<Customer> {
    // 1. Validation
    await this.validateCustomerData(customerData);
    
    // 2. Business logic
    const customer = this.customerRepository.create(customerData);
    
    // 3. Database operation
    const savedCustomer = await this.customerRepository.save(customer);
    
    // 4. Return result
    return savedCustomer;
}
```

### Controller Method Structure
```typescript
createCustomer = async (req: Request, res: Response): Promise<void> => {
    try {
        // 1. Extract and validate request data
        const customerData: CreateCustomerRequest = req.body;
        
        // 2. Call service method
        const customer = await this.customerService.createCustomer(customerData);
        
        // 3. Format and send response
        res.status(201).json({
            success: true,
            data: customer,
            message: "Customer created successfully"
        });
    } catch (error) {
        // 4. Handle errors consistently
        console.error("Error creating customer:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create customer"
        });
    }
};
```

## 🔧 Tools and Dependencies

### Essential Dependencies
```json
{
  "dependencies": {
    "express": "^5.1.0",
    "typeorm": "^0.3.24",
    "mysql2": "^3.14.1",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "reflect-metadata": "^0.2.2"
  },
  "devDependencies": {
    "@types/express": "^5.0.2",
    "@types/cors": "^2.8.18",
    "@types/node": "^22.15.28",
    "typescript": "^5.8.3",
    "ts-node-dev": "^2.0.0"
  }
}
```

### Development Scripts
```json
{
  "scripts": {
    "dev": "ts-node-dev --respawn --transpile-only src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js"
  }
}
```

## 📚 Additional Recommendations

### 1. Environment Configuration
- Use `.env` files for configuration
- Never commit sensitive data to version control
- Use different configs for development/production

### 2. Logging Strategy
- Use structured logging (consider winston or similar)
- Log errors with context information
- Implement request/response logging for debugging

### 3. Security Considerations
- Validate all input data
- Use parameterized queries (TypeORM handles this)
- Implement proper authentication/authorization
- Add rate limiting for API endpoints

### 4. Documentation
- Document all API endpoints
- Add JSDoc comments for complex business logic
- Maintain this development guide as project evolves

---

**Remember:** Consistency is key! Follow these patterns across all modules for maintainable, scalable backend architecture.
