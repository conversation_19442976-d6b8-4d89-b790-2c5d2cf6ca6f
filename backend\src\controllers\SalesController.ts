import { Request, Response } from "express";
import { AppDataSource } from "../dbconfig";
import { SalesService, CreateSaleRequest } from "../services/SalesService";
import { CustomerService } from "../services/CustomerService";

export class SalesController {
    private salesService: SalesService;
    private customerService: CustomerService;

    constructor() {
        this.salesService = new SalesService(AppDataSource);
        this.customerService = new CustomerService(AppDataSource);
    }

    // Create a new sale
    createSale = async (req: Request, res: Response): Promise<void> => {
        try {
            const saleData: CreateSaleRequest = req.body;

            // Validate required fields
            if (!saleData.items || saleData.items.length === 0) {
                res.status(400).json({
                    success: false,
                    message: "At least one item is required"
                });
                return;
            }

            if (!saleData.paymentMethod) {
                res.status(400).json({
                    success: false,
                    message: "Payment method is required"
                });
                return;
            }

            // Validate credit sales have customer
            if (saleData.paymentMethod === "Credit" && !saleData.customerId) {
                res.status(400).json({
                    success: false,
                    message: "Customer is required for credit sales"
                });
                return;
            }

            const sale = await this.salesService.createSale(saleData);

            res.status(201).json({
                success: true,
                message: "Sale created successfully",
                data: sale
            });

        } catch (error) {
            console.error("Error creating sale:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to create sale"
            });
        }
    };

    // Get sales history with pagination and filters
    getSalesHistory = async (req: Request, res: Response): Promise<void> => {
        try {
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;
            
            const filters: any = {};
            
            if (req.query.startDate) {
                filters.startDate = new Date(req.query.startDate as string);
            }
            
            if (req.query.endDate) {
                filters.endDate = new Date(req.query.endDate as string);
            }
            
            if (req.query.customerId) {
                filters.customerId = parseInt(req.query.customerId as string);
            }
            
            if (req.query.paymentMethod) {
                filters.paymentMethod = req.query.paymentMethod as string;
            }
            
            if (req.query.status) {
                filters.status = req.query.status as string;
            }

            const result = await this.salesService.getSalesHistory(page, limit, filters);

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error("Error fetching sales history:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch sales history"
            });
        }
    };

    // Get sale by ID
    getSaleById = async (req: Request, res: Response): Promise<void> => {
        try {
            const id = parseInt(req.params.id);
            
            if (isNaN(id)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid sale ID"
                });
                return;
            }

            const sale = await this.salesService.getSaleById(id);

            if (!sale) {
                res.status(404).json({
                    success: false,
                    message: "Sale not found"
                });
                return;
            }

            res.json({
                success: true,
                data: sale
            });

        } catch (error) {
            console.error("Error fetching sale:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch sale"
            });
        }
    };

    // Get daily sales summary
    getDailySummary = async (req: Request, res: Response): Promise<void> => {
        try {
            let date: Date | undefined;
            
            if (req.query.date) {
                date = new Date(req.query.date as string);
                if (isNaN(date.getTime())) {
                    res.status(400).json({
                        success: false,
                        message: "Invalid date format"
                    });
                    return;
                }
            }

            const summary = await this.salesService.getDailySummary(date);

            res.json({
                success: true,
                data: summary
            });

        } catch (error) {
            console.error("Error fetching daily summary:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch daily summary"
            });
        }
    };

    // Cancel a sale
    cancelSale = async (req: Request, res: Response): Promise<void> => {
        try {
            const id = parseInt(req.params.id);
            const { reason } = req.body;

            if (isNaN(id)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid sale ID"
                });
                return;
            }

            if (!reason) {
                res.status(400).json({
                    success: false,
                    message: "Cancellation reason is required"
                });
                return;
            }

            const sale = await this.salesService.cancelSale(id, reason);

            res.json({
                success: true,
                message: "Sale cancelled successfully",
                data: sale
            });

        } catch (error) {
            console.error("Error cancelling sale:", error);
            res.status(500).json({
                success: false,
                message: error instanceof Error ? error.message : "Failed to cancel sale"
            });
        }
    };

    // Get sales analytics/reports
    getSalesAnalytics = async (req: Request, res: Response): Promise<void> => {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
            const reportType = req.query.reportType as string || "overview";

            // For now, return daily summary as base analytics
            // This can be expanded with more complex analytics
            const summary = await this.salesService.getDailySummary(startDate);

            res.json({
                success: true,
                data: {
                    reportType,
                    period: {
                        startDate,
                        endDate
                    },
                    summary
                }
            });

        } catch (error) {
            console.error("Error fetching sales analytics:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch sales analytics"
            });
        }
    };

    // Search customers for sale creation
    searchCustomers = async (req: Request, res: Response): Promise<void> => {
        try {
            const { query } = req.query;

            if (!query || typeof query !== "string") {
                res.status(400).json({
                    success: false,
                    message: "Search query is required"
                });
                return;
            }

            // Search by phone first, then by name
            let customers = await this.customerService.getCustomersByPhone(query);
            
            if (customers.length === 0) {
                customers = await this.customerService.getCustomersByName(query);
            }

            res.json({
                success: true,
                data: customers
            });

        } catch (error) {
            console.error("Error searching customers:", error);
            res.status(500).json({
                success: false,
                message: "Failed to search customers"
            });
        }
    };

    // Get today's sales for dashboard
    getTodaysSales = async (req: Request, res: Response): Promise<void> => {
        try {
            const today = new Date();
            const summary = await this.salesService.getDailySummary(today);

            // Get recent sales for today
            const salesHistory = await this.salesService.getSalesHistory(1, 10, {
                startDate: today,
                endDate: today,
                status: "Completed"
            });

            res.json({
                success: true,
                data: {
                    summary,
                    recentSales: salesHistory.sales
                }
            });

        } catch (error) {
            console.error("Error fetching today's sales:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch today's sales"
            });
        }
    };

    // Get sales by date range for reports
    getSalesByDateRange = async (req: Request, res: Response): Promise<void> => {
        try {
            const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
            const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 50;

            if (!startDate || !endDate) {
                res.status(400).json({
                    success: false,
                    message: "Start date and end date are required"
                });
                return;
            }

            const result = await this.salesService.getSalesHistory(page, limit, {
                startDate,
                endDate,
                status: "Completed"
            });

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error("Error fetching sales by date range:", error);
            res.status(500).json({
                success: false,
                message: "Failed to fetch sales by date range"
            });
        }
    };
}
