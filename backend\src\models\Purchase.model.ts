import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    <PERSON><PERSON><PERSON><PERSON><PERSON>n,
    CreateDateColumn,
} from "typeorm";
import { Product } from "./Product.model";
import { Suppliers } from "./Suppliers.model";
import { Units } from "./Units.model";


@Entity("purchases")
export class Purchase {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Product)
    @JoinColumn({ name: "product_id" })
    product!: Product;

    @ManyToOne(() => Suppliers, { nullable: true })
    @JoinColumn({ name: "supplier_id" })
    supplier!: Suppliers;

    @ManyToOne(() => Units)
    @JoinColumn({ name: "unit_id" })
    unit!: Units;

    @Column({ nullable: true })
    invoiceNumber!: string;

    @Column("decimal", { precision: 10, scale: 2, nullable: true })
    totalAmount!: number;

    @Column({ type: "text", nullable: true })
    desc!: string;

    @Column()
    quantity!: number;

    @CreateDateColumn({ name: "purchased_at", type: "datetime", default: () => "CURRENT_TIMESTAMP" })
    purchasedAt!: Date;
}
