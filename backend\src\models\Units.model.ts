import { Entity, Column, PrimaryGeneratedColumn } from "typeorm";

@Entity('units')
export class Units {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "varchar", length: 50, unique: true })
    name!: string;

    @Column({ type: "tinyint", width: 1, default: 1 })
    status!: number;

    @Column({ type: "text", nullable: true, name: "desc" })
    description?: string;

    @Column({ type: "datetime", default: () => "CURRENT_TIMESTAMP" })
    datetime!: Date;
}