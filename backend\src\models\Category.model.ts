import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity('product_categories')
export class Category {
    
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "varchar", length: 100, unique: true })
    name!: string;

    @Column({ type: "text", nullable: true, name: "desc" })
    description?: string;

    @Column({ type: "tinyint", width: 1, default: 1 })
    status!: number;

}