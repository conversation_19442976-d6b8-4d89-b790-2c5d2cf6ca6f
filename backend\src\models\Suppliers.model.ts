import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
} from "typeorm";

@Entity("suppliers")
export class Suppliers {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "varchar", length: 100 })
    name!: string;

    @Column({ type: "varchar", length: 45 })
    phone!: string;

    @Column({ type: "text", nullable: true })
    address?: string;

    @Column({ type: "varchar", length: 45, nullable: true, name: "gst_number" })
    gstNumber?: string;

    @Column({ type: "int", nullable: false, default: 0, name: "totalPurchases" })
    totalPurchases!: number;

    @Column({ type: "decimal", precision: 10, scale: 2, nullable: false, default: 0, name: "totalAmount" })
    totalAmount!: number;

    @Column({
        type: "datetime",
        nullable: false,
        name: "lastPurchase",
        default: () => "CURRENT_TIMESTAMP",
    })
    lastPurchase!: Date;

    @Column({ type: "tinyint", nullable: false, default: 0 })
    status!: number;

    @Column({
        type: "datetime",
        nullable: false,
        name: "datetime",
        default: () => "CURRENT_TIMESTAMP",
    })
    datetime!: Date;
}
