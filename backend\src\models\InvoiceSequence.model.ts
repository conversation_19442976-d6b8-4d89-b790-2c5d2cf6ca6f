import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
} from "typeorm";

@Entity("invoice_sequences")
export class InvoiceSequence {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({
        type: "enum",
        enum: ["Invoice", "Bill", "Credit_Note"],
        unique: true,
        name: "sequence_type"
    })
    sequenceType!: "Invoice" | "Bill" | "Credit_Note";

    @Column({ type: "varchar", length: 10 })
    prefix!: string;

    @Column({ type: "int", default: 1, name: "current_number" })
    currentNumber!: number;

    @Column({ type: "varchar", length: 20, default: "YYMMDD", name: "date_format" })
    dateFormat!: string;

    @Column({
        type: "enum",
        enum: ["Daily", "Monthly", "Yearly", "Never"],
        default: "Daily",
        name: "reset_frequency"
    })
    resetFrequency!: "Daily" | "Monthly" | "Yearly" | "Never";

    @Column({ type: "date", nullable: true, name: "last_reset_date" })
    lastResetDate?: Date;

    @CreateDateColumn({
        name: "created_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    createdAt!: Date;

    @UpdateDateColumn({
        name: "updated_at",
        type: "datetime",
        default: () => "CURRENT_TIMESTAMP"
    })
    updatedAt!: Date;

    // Methods for generating invoice numbers
    generateInvoiceNumber(): string {
        const today = new Date();
        let dateString = "";

        // Format date based on dateFormat
        switch (this.dateFormat) {
            case "YYMMDD":
                dateString = today.toISOString().slice(2, 10).replace(/-/g, "");
                break;
            case "YYYYMMDD":
                dateString = today.toISOString().slice(0, 10).replace(/-/g, "");
                break;
            case "DDMMYY":
                const day = today.getDate().toString().padStart(2, "0");
                const month = (today.getMonth() + 1).toString().padStart(2, "0");
                const year = today.getFullYear().toString().slice(-2);
                dateString = `${day}${month}${year}`;
                break;
            case "MMYY":
                const monthStr = (today.getMonth() + 1).toString().padStart(2, "0");
                const yearStr = today.getFullYear().toString().slice(-2);
                dateString = `${monthStr}${yearStr}`;
                break;
            default:
                dateString = today.toISOString().slice(2, 10).replace(/-/g, "");
        }

        // Check if reset is needed
        if (this.shouldReset(today)) {
            this.currentNumber = 1;
            this.lastResetDate = today;
        }

        const paddedNumber = this.currentNumber.toString().padStart(4, "0");
        return `${this.prefix}${dateString}${paddedNumber}`;
    }

    private shouldReset(currentDate: Date): boolean {
        if (!this.lastResetDate || this.resetFrequency === "Never") {
            return false;
        }

        const lastReset = new Date(this.lastResetDate);

        switch (this.resetFrequency) {
            case "Daily":
                return currentDate.toDateString() !== lastReset.toDateString();
            case "Monthly":
                return (
                    currentDate.getMonth() !== lastReset.getMonth() ||
                    currentDate.getFullYear() !== lastReset.getFullYear()
                );
            case "Yearly":
                return currentDate.getFullYear() !== lastReset.getFullYear();
            default:
                return false;
        }
    }

    incrementNumber(): void {
        this.currentNumber += 1;
    }

    // Static method to get next invoice number for a specific type
    static async getNextInvoiceNumber(
        sequenceType: "Invoice" | "Bill" | "Credit_Note"
    ): Promise<string> {
        // This would be implemented in the service layer
        // Placeholder for now
        return "";
    }
}
