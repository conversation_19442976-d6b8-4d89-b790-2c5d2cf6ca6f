import {
    <PERSON>tity,
    PrimaryGeneratedC<PERSON>umn,
    Column,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    Index,
} from "typeorm";
import { Sales } from "./Sales.model";

@Entity("payment_transactions")
export class PaymentTransaction {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Sales, (sale) => sale.paymentTransactions, { onDelete: "CASCADE" })
    @JoinColumn({ name: "sale_id" })
    @Index("idx_payment_sale")
    sale!: Sales;

    @Column({
        type: "enum",
        enum: ["Cash", "UPI", "Credit", "Card"],
        name: "payment_method"
    })
    @Index("idx_payment_method")
    paymentMethod!: "Cash" | "UPI" | "Credit" | "Card";

    @Column("decimal", { precision: 10, scale: 2 })
    amount!: number;

    @Column({ type: "varchar", length: 100, nullable: true, name: "transaction_id" })
    transactionId?: string;

    @Column({ type: "varchar", length: 100, nullable: true, name: "upi_id" })
    upiId?: string;

    @Column({ type: "varchar", length: 100, nullable: true, name: "reference_number" })
    referenceNumber?: string;

    @Column({
        type: "enum",
        enum: ["Success", "Failed", "Pending"],
        default: "Success",
        name: "payment_status"
    })
    paymentStatus!: "Success" | "Failed" | "Pending";

    @Column({ type: "datetime", default: () => "CURRENT_TIMESTAMP", name: "payment_date" })
    @Index("idx_payment_date")
    paymentDate!: Date;

    @Column({ type: "text", nullable: true })
    notes?: string;

    // Virtual properties for API responses
    get isDigitalPayment(): boolean {
        return this.paymentMethod === "UPI" || this.paymentMethod === "Card";
    }

    get hasReference(): boolean {
        return !!(this.transactionId || this.referenceNumber || this.upiId);
    }

    get paymentMethodDisplay(): string {
        switch (this.paymentMethod) {
            case "UPI":
                return this.upiId ? `UPI (${this.upiId})` : "UPI";
            case "Card":
                return this.referenceNumber ? `Card (${this.referenceNumber})` : "Card";
            case "Cash":
                return "Cash";
            case "Credit":
                return "Credit/Udhaar";
            default:
                return this.paymentMethod;
        }
    }
}
